// Middleware pour vérifier les rôles d'utilisateur
const roleMiddleware = (allowedRoles) => {
  return (req, res, next) => {
    try {
      // Vérifier si l'utilisateur est authentifié
      if (!req.utilisateur) {
        return res.status(401).json({
          message: 'Accès non autorisé. Authentification requise.'
        });
      }

      // Vérifier si l'utilisateur a un rôle
      if (!req.utilisateur.role) {
        return res.status(403).json({
          message: 'Accès refusé. Rôle utilisateur non défini.'
        });
      }

      // Vérifier si le rôle de l'utilisateur est dans la liste des rôles autorisés
      if (!allowedRoles.includes(req.utilisateur.role)) {
        return res.status(403).json({
          message: `Accès refusé. Rôle requis: ${allowedRoles.join(' ou ')}. Votre rôle: ${req.utilisateur.role}`
        });
      }

      // Si tout est OK, passer au middleware suivant
      next();
    } catch (error) {
      console.error('Erreur dans roleMiddleware:', error);
      return res.status(500).json({
        message: 'Erreur serveur lors de la vérification des permissions'
      });
    }
  };
};

export default roleMiddleware;

import React, { useState } from 'react';
import { FaTimes, FaTasks, FaSave } from 'react-icons/fa';
import { useEmployees } from '../../hooks/useEmployees';
import { tacheService } from '../../services/tacheService';

const AddTacheModal = ({ projet, onClose, onCreate }) => {
  const { employees } = useEmployees();
  const [formData, setFormData] = useState({
    nom_tache: '',
    description: '',
    date_debut: '',
    date_fin_prevue: '',
    employe_assigne: '',
    priorite: 'Moyenne',
    temps_estime: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const priorites = tacheService.getPriorites();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (!formData.nom_tache.trim()) {
      setError('Le nom de la tâche est requis');
      return;
    }
    if (!formData.description.trim()) {
      setError('La description est requise');
      return;
    }
    if (!formData.date_debut) {
      setError('La date de début est requise');
      return;
    }
    if (!formData.date_fin_prevue) {
      setError('La date de fin prévue est requise');
      return;
    }
    if (!formData.employe_assigne) {
      setError('L\'employé assigné est requis');
      return;
    }

    // Validation des dates
    if (new Date(formData.date_fin_prevue) <= new Date(formData.date_debut)) {
      setError('La date de fin prévue doit être postérieure à la date de début');
      return;
    }

    // Validation des dates par rapport au projet
    const projetDebut = new Date(projet.date_debut);
    const projetFin = new Date(projet.date_fin_prevue);
    const tacheDebut = new Date(formData.date_debut);
    const tacheFin = new Date(formData.date_fin_prevue);

    if (tacheDebut < projetDebut || tacheFin > projetFin) {
      setError('Les dates de la tâche doivent être dans la plage du projet');
      return;
    }

    // Validation du temps estimé
    if (formData.temps_estime && parseFloat(formData.temps_estime) < 0) {
      setError('Le temps estimé doit être positif');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const tacheData = {
        nom_tache: formData.nom_tache.trim(),
        description: formData.description.trim(),
        date_debut: formData.date_debut,
        date_fin_prevue: formData.date_fin_prevue,
        employe_assigne: formData.employe_assigne,
        priorite: formData.priorite,
        temps_estime: formData.temps_estime ? parseFloat(formData.temps_estime) : 0,
        projet: projet._id
      };

      await onCreate(tacheData);
      onClose();
    } catch (error) {
      setError(error.message || 'Erreur lors de la création de la tâche');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1100,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '600px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}>
        {/* En-tête */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '1.5rem',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '8px',
              backgroundColor: '#10b981',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white'
            }}>
              <FaTasks />
            </div>
            <div>
              <h2 style={{
                margin: 0,
                fontSize: '1.5rem',
                fontWeight: '600',
                color: '#1f2937'
              }}>
                Nouvelle tâche
              </h2>
              <p style={{
                margin: '0.25rem 0 0 0',
                fontSize: '0.875rem',
                color: '#6b7280'
              }}>
                Projet: {projet?.nom_projet}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              color: '#6b7280',
              cursor: 'pointer',
              padding: '0.5rem',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#f3f4f6'}
            onMouseOut={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <FaTimes size={20} />
          </button>
        </div>

        {/* Contenu */}
        <form onSubmit={handleSubmit}>
          <div style={{ padding: '1.5rem' }}>
            {error && (
              <div style={{
                backgroundColor: '#fef2f2',
                border: '1px solid #fecaca',
                borderRadius: '8px',
                padding: '0.75rem',
                marginBottom: '1rem',
                color: '#dc2626',
                fontSize: '0.875rem'
              }}>
                {error}
              </div>
            )}

            {/* Nom de la tâche */}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Nom de la tâche *
              </label>
              <input
                type="text"
                name="nom_tache"
                value={formData.nom_tache}
                onChange={handleChange}
                placeholder="Ex: Développement de l'interface utilisateur"
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  outline: 'none',
                  transition: 'border-color 0.2s',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => e.target.style.borderColor = '#10b981'}
                onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
              />
            </div>

            {/* Description */}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Description détaillée de la tâche..."
                rows={3}
                required
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  outline: 'none',
                  transition: 'border-color 0.2s',
                  resize: 'vertical',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => e.target.style.borderColor = '#10b981'}
                onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
              />
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              {/* Date de début */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Date de début *
                </label>
                <input
                  type="date"
                  name="date_debut"
                  value={formData.date_debut}
                  onChange={handleChange}
                  min={projet?.date_debut?.split('T')[0]}
                  max={projet?.date_fin_prevue?.split('T')[0]}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#10b981'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>

              {/* Date de fin prévue */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Date de fin prévue *
                </label>
                <input
                  type="date"
                  name="date_fin_prevue"
                  value={formData.date_fin_prevue}
                  onChange={handleChange}
                  min={formData.date_debut || projet?.date_debut?.split('T')[0]}
                  max={projet?.date_fin_prevue?.split('T')[0]}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#10b981'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '1rem' }}>
              {/* Employé assigné */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Employé assigné *
                </label>
                <select
                  name="employe_assigne"
                  value={formData.employe_assigne}
                  onChange={handleChange}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#10b981'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                >
                  <option value="">Sélectionner un employé</option>
                  {employees.map((emp) => (
                    <option key={emp._id} value={emp._id}>
                      {emp.prenom} {emp.nom} - {emp.poste}
                    </option>
                  ))}
                </select>
              </div>

              {/* Priorité */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Priorité
                </label>
                <select
                  name="priorite"
                  value={formData.priorite}
                  onChange={handleChange}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    backgroundColor: 'white',
                    cursor: 'pointer',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#10b981'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                >
                  {priorites.map((priorite) => (
                    <option key={priorite.value} value={priorite.value}>
                      {priorite.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Temps estimé */}
              <div style={{ marginBottom: '1rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.5rem'
                }}>
                  Temps estimé (heures)
                </label>
                <input
                  type="number"
                  name="temps_estime"
                  value={formData.temps_estime}
                  onChange={handleChange}
                  placeholder="Ex: 8"
                  min="0"
                  step="0.5"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#10b981'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
              </div>
            </div>

            {/* Information sur les contraintes du projet */}
            <div style={{
              backgroundColor: '#f0f9ff',
              border: '1px solid #bae6fd',
              borderRadius: '8px',
              padding: '0.75rem',
              fontSize: '0.875rem',
              color: '#0369a1'
            }}>
              <strong>📅 Contraintes du projet :</strong><br />
              Période : du {new Date(projet?.date_debut).toLocaleDateString('fr-FR')} au {new Date(projet?.date_fin_prevue).toLocaleDateString('fr-FR')}<br />
              Chef de projet : {projet?.chef_projet?.prenom} {projet?.chef_projet?.nom}
            </div>
          </div>

          {/* Pied de page */}
          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '0.75rem',
            padding: '1.5rem',
            borderTop: '1px solid #e5e7eb',
            backgroundColor: '#f9fafb'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              style={{
                backgroundColor: 'white',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: loading ? 'not-allowed' : 'pointer',
                transition: 'background-color 0.2s',
                opacity: loading ? 0.6 : 1
              }}
              onMouseOver={(e) => !loading && (e.target.style.backgroundColor = '#f9fafb')}
              onMouseOut={(e) => !loading && (e.target.style.backgroundColor = 'white')}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={loading || !formData.nom_tache || !formData.description || !formData.date_debut || !formData.date_fin_prevue || !formData.employe_assigne}
              style={{
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: (loading || !formData.nom_tache || !formData.description || !formData.date_debut || !formData.date_fin_prevue || !formData.employe_assigne) ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                transition: 'background-color 0.2s',
                opacity: (loading || !formData.nom_tache || !formData.description || !formData.date_debut || !formData.date_fin_prevue || !formData.employe_assigne) ? 0.6 : 1
              }}
              onMouseOver={(e) => {
                if (!loading && formData.nom_tache && formData.description && formData.date_debut && formData.date_fin_prevue && formData.employe_assigne) {
                  e.target.style.backgroundColor = '#059669';
                }
              }}
              onMouseOut={(e) => {
                if (!loading && formData.nom_tache && formData.description && formData.date_debut && formData.date_fin_prevue && formData.employe_assigne) {
                  e.target.style.backgroundColor = '#10b981';
                }
              }}
            >
              <FaSave />
              {loading ? 'Création...' : 'Créer la tâche'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddTacheModal;

import mongoose from "mongoose";

const departementSchema = new mongoose.Schema({
  nom_departement: { type: String, required: true, unique: true, index: true },
  description: { type: String },
  responsable: { type: mongoose.Schema.Types.ObjectId, ref: "Employe" },
  is_active: { type: Boolean, default: true }
}, { timestamps: true });

const Departement = mongoose.model("Departement", departementSchema);
export default Departement;
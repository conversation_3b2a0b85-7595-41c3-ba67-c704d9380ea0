import mongoose from 'mongoose';

const candidatureFormationSchema = new mongoose.Schema({
  formation_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Formation',
    required: true
  },
  employe_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Employe',
    required: true
  },
  statut: {
    type: String,
    enum: ['en_attente', 'approuvee', 'refusee'],
    default: 'en_attente'
  },
  date_candidature: {
    type: Date,
    default: Date.now
  },
  date_reponse: {
    type: Date
  },
  message_employe: {
    type: String,
    maxlength: 500
  },
  message_rh: {
    type: String,
    maxlength: 500
  },
  vue_par_rh: {
    type: Boolean,
    default: false
  },
  traite_par: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Utilisateur'
  },
  notification_employe_vue: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Index pour éviter les doublons (un employé ne peut postuler qu'une fois à la même formation)
candidatureFormationSchema.index({ formation_id: 1, employe_id: 1 }, { unique: true });

const CandidatureFormation = mongoose.model('CandidatureFormation', candidatureFormationSchema);

export default CandidatureFormation;

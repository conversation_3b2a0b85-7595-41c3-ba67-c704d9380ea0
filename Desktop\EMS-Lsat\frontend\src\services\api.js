/**
 * Configuration de base pour les appels API
 */
import axios from 'axios';

// Configuration de base d'Axios
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:4000/api';

// Instance Axios avec configuration de base
const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Intercepteur pour gérer les réponses et erreurs
api.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        // Gestion des erreurs globales
        if (error.response?.status === 401) {
            // Token expiré ou invalide
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = '/login';
        }
        
        // Log des erreurs en développement
        if (import.meta.env.DEV) {
            console.error('API Error:', error.response?.data || error.message);
        }
        
        return Promise.reject(error);
    }
);

export default api;

const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  expediteur: { type: mongoose.Schema.Types.ObjectId, ref: "utilisateurs" },
  destinataire: { type: mongoose.Schema.Types.ObjectId, ref: "utilisateurs" },
  message: String,
  type: { type: String, enum: ["conge", "salaire", "tache", "general"] },
  lu: { type: Boolean, default: false },
  date_envoi: { type: Date, default: Date.now }
}, { timestamps: true });

module.exports = mongoose.model("Message", messageSchema);


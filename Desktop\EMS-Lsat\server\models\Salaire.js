const mongoose = require("mongoose");

const salaireSchema = new mongoose.Schema({
  employe: { type: mongoose.Schema.Types.ObjectId, ref: "employes" },
  mois: String,
  salaire_base: Number,
  total_primes: Number,
  total_heures_supp: Number,
  montant_heures_supp: Number,
  avantages: Number,
  deductions: Number,
  retenues: Number,
  salaire_net: Number,
  date_versement: Date
});

module.exports = mongoose.model("Salaire", salaireSchema);

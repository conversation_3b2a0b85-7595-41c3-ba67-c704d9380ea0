/**
 * Constantes pour les routes de l'application
 */

export const ROUTES = {
    // Routes publiques
    HOME: '/',
    LOGIN: '/login',
    
    // Routes protégées
    DASHBOARD: '/dashboard',
    
    // Routes RH/Admin
    RH_PAGE: '/rh-page',
    ADMIN_PAGE: '/admin-page',
    
    // Routes employés
    EMPLOYEE_PAGE: '/employee-page',
    EMPLOYEE_PROFILE: '/employee-profile',
    
    // Routes de gestion
    EMPLOYEES: '/employees',
    DEPARTMENTS: '/departments',
    USERS: '/users',
    
    // Routes système
    SETTINGS: '/settings',
    LOGS: '/logs'
};

export const ROUTE_LABELS = {
    [ROUTES.HOME]: 'Accueil',
    [ROUTES.LOGIN]: 'Connexion',
    [ROUTES.DASHBOARD]: 'Tableau de bord',
    [ROUTES.RH_PAGE]: 'Ressources Humaines',
    [ROUTES.ADMIN_PAGE]: 'Administration',
    [ROUTES.EMPLOYEE_PAGE]: 'Espace Employé',
    [ROUTES.EMPLOYEE_PROFILE]: 'Mon Profil',
    [ROUTES.EMPLOYEES]: 'Employés',
    [ROUTES.DEPARTMENTS]: 'Départements',
    [ROUTES.USERS]: 'Utilisateurs',
    [ROUTES.SETTINGS]: 'Paramètres',
    [ROUTES.LOGS]: 'Journaux'
};

/**
 * Routes accessibles selon le rôle
 */
export const ROLE_ROUTES = {
    admin: [
        ROUTES.DASHBOARD,
        ROUTES.ADMIN_PAGE,
        ROUTES.RH_PAGE,
        ROUTES.EMPLOYEES,
        ROUTES.DEPARTMENTS,
        ROUTES.USERS,
        ROUTES.SETTINGS,
        ROUTES.LOGS
    ],
    RH: [
        ROUTES.DASHBOARD,
        ROUTES.RH_PAGE,
        ROUTES.EMPLOYEES,
        ROUTES.DEPARTMENTS,
        ROUTES.USERS
    ],
    employee: [
        ROUTES.DASHBOARD,
        ROUTES.EMPLOYEE_PAGE,
        ROUTES.EMPLOYEE_PROFILE
    ]
};

/**
 * Vérifier si une route est accessible pour un rôle
 * @param {string} route - Route à vérifier
 * @param {string} role - Rôle de l'utilisateur
 * @returns {boolean} True si accessible
 */
export const isRouteAccessible = (route, role) => {
    if (!role) return false;
    return ROLE_ROUTES[role]?.includes(route) || false;
};

/**
 * Obtenir le label d'une route
 * @param {string} route - Route
 * @returns {string} Label de la route
 */
export const getRouteLabel = (route) => {
    return ROUTE_LABELS[route] || route;
};

import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../context/authContext';
import { useLocation, useNavigate } from 'react-router-dom';
import useFormationNotifications from '../../hooks/useFormationNotifications';
import {
  FaBell,
  FaSearch,
  FaUser,
  FaSignOutAlt,
  FaCog,
  FaChevronDown,
  FaExclamationTriangle,
  FaHome,
  FaUsers,
  FaBuilding,
  FaProjectDiagram,
  FaFileContract,
  FaCalendarAlt,
  FaFileAlt,
  FaArchive,
  FaCrown,
  FaUserTie,
  FaGraduationCap
} from 'react-icons/fa';

const ModernNavbar = () => {
  const { utilisateur, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showAlerts, setShowAlerts] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const navbarRef = useRef(null);

  // Hook pour les notifications de formations (RH seulement)
  const {
    notifications: formationNotifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    canViewNotifications
  } = useFormationNotifications();

  // Configuration des pages selon le rôle et la route
  const getPageInfo = () => {
    const path = location.pathname;
    const role = utilisateur?.role;

    // Configuration pour Admin
    if (role === 'admin') {
      if (path.includes('/admin/dashboard')) return { title: 'Dashboard', subtitle: 'Vue d\'ensemble administrative', icon: FaHome };
      if (path.includes('/admin/departments')) return { title: 'Départements', subtitle: 'Gestion des départements', icon: FaBuilding };
      if (path.includes('/admin/services')) return { title: 'Services', subtitle: 'Gestion des services', icon: FaCog };
      if (path.includes('/admin/contracts')) return { title: 'Contrats', subtitle: 'Gestion des contrats', icon: FaFileContract };
      if (path.includes('/admin/projects')) return { title: 'Projets', subtitle: 'Gestion des projets', icon: FaProjectDiagram };
      if (path.includes('/admin/archives')) return { title: 'Archives', subtitle: 'Projets archivés', icon: FaArchive };
      return { title: 'Admin Panel', subtitle: 'Administration système', icon: FaCrown };
    }

    // Configuration pour RH
    if (role === 'rh') {
      if (path.includes('/RHpage/employes')) return { title: 'Employés', subtitle: 'Gestion des employés', icon: FaUsers };
      if (path.includes('/RHpage/departements')) return { title: 'Départements', subtitle: 'Gestion des départements', icon: FaBuilding };
      if (path.includes('/RHpage/conges')) return { title: 'Congés', subtitle: 'Gestion des congés', icon: FaCalendarAlt };
      if (path.includes('/RHpage/contrats')) return { title: 'Contrats', subtitle: 'Gestion des contrats', icon: FaFileContract };
      if (path.includes('/RHpage/formations')) return { title: 'Formations', subtitle: 'Gestion des formations', icon: FaGraduationCap };
      if (path.includes('/RHpage/documents')) return { title: 'Documents', subtitle: 'Gestion documentaire', icon: FaFileAlt };
      if (path.includes('/RHpage')) return { title: 'Dashboard RH', subtitle: 'Vue d\'ensemble RH', icon: FaHome };
      return { title: 'RH Dashboard', subtitle: 'Ressources Humaines', icon: FaUserTie };
    }

    // Configuration pour Employee
    if (role === 'employee') {
      if (path.includes('/employee/dashboard')) return { title: 'Mon Espace', subtitle: 'Tableau de bord personnel', icon: FaHome };
      if (path.includes('/employee/profile')) return { title: 'Mon Profil', subtitle: 'Informations personnelles', icon: FaUser };
      if (path.includes('/employee/leaves')) return { title: 'Mes Congés', subtitle: 'Demandes de congés', icon: FaCalendarAlt };
      if (path.includes('/employee/projects')) return { title: 'Mes Projets', subtitle: 'Projets et tâches', icon: FaProjectDiagram };
      if (path.includes('/employee/documents')) return { title: 'Mes Documents', subtitle: 'Attestations et documents', icon: FaFileAlt };
      return { title: 'Espace Employé', subtitle: 'Tableau de bord', icon: FaUser };
    }

    return { title: 'EMS', subtitle: 'Système de gestion', icon: FaHome };
  };

  const pageInfo = getPageInfo();

  const handleLogout = () => {
    logout();
  };

  /**
   * Fonction pour gérer le clic sur une notification de formation
   * Marque la notification comme vue et navigue vers l'interface formations RH
   * @param {Object} notification - La notification cliquée
   */
  const handleNotificationClick = async (notification) => {
    try {
      // Marquer la notification comme vue
      await markAsRead(notification._id);

      // Fermer le dropdown des notifications
      setShowNotifications(false);

      // Naviguer vers l'interface formations RH
      if (utilisateur?.role === 'rh') {
        navigate('/RHpage/formations');
      } else if (utilisateur?.role === 'admin') {
        navigate('/AdminPage/formations');
      }

      console.log(`🔔 Navigation vers formations pour notification: ${notification._id}`);
    } catch (error) {
      console.error('❌ Erreur lors du clic sur notification:', error);
    }
  };

  // Gestion de la recherche avec raccourci clavier Ctrl+K
  useEffect(() => {
    const handleKeyDown = (event) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        document.getElementById('navbar-search')?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (navbarRef.current && !navbarRef.current.contains(event.target)) {
        setShowUserMenu(false);
        setShowNotifications(false);
        setShowAlerts(false);
        setShowSearchResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fonction de recherche contextuelle
  const handleSearch = (query) => {
    setSearchQuery(query);
    if (query.length > 2) {
      setShowSearchResults(true);
      // TODO: Implémenter la logique de recherche selon le contexte
    } else {
      setShowSearchResults(false);
    }
  };

  // Obtenir les alertes selon le rôle
  const getAlerts = () => {
    const role = utilisateur?.role;
    if (role === 'admin') {
      return [
        { id: 1, type: 'warning', message: '3 projets nécessitent votre attention', urgent: true },
        { id: 2, type: 'info', message: 'Mise à jour système disponible', urgent: false }
      ];
    }
    if (role === 'rh') {
      return [
        { id: 1, type: 'warning', message: '5 demandes de congé en attente', urgent: true },
        { id: 2, type: 'info', message: '2 nouveaux employés à intégrer', urgent: false }
      ];
    }
    if (role === 'employee') {
      return [
        { id: 1, type: 'info', message: 'Votre demande de congé a été approuvée', urgent: false }
      ];
    }
    return [];
  };

  const alerts = getAlerts();
  const hasUrgentAlerts = alerts.some(alert => alert.urgent);

  return (
    <nav
      ref={navbarRef}
      style={{
        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
        borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
        height: '4.5rem',
        position: 'fixed',
        top: 0,
        left: '280px', // Largeur par défaut du sidebar ouvert
        right: 0,
        zIndex: 999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 2rem',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        backdropFilter: 'blur(10px)',
        transition: 'left 0.3s ease',
        fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
      }}
    >
      {/* Left side - [Titre Page] → [sous-titre] */}
      <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          {/* Icône de la page */}
          <div style={{
            width: '40px',
            height: '40px',
            borderRadius: '10px',
            backgroundColor: utilisateur?.role === 'admin' ? '#7c3aed' :
                           utilisateur?.role === 'rh' ? '#3b82f6' : '#667eea',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }}>
            <pageInfo.icon style={{ fontSize: '1.2rem' }} />
          </div>

          {/* Titre et sous-titre */}
          <div>
            <h1 style={{
              margin: 0,
              fontSize: '1.5rem',
              fontWeight: '700',
              color: '#1f2937',
              lineHeight: '1.2'
            }}>
              {pageInfo.title}
            </h1>
            <p style={{
              margin: 0,
              fontSize: '0.875rem',
              color: '#6b7280',
              fontWeight: '500'
            }}>
              {pageInfo.subtitle}
            </p>
          </div>
        </div>
      </div>

      {/* Right side - [Recherche] [Alerte] [Notifications] [Profil] */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>

        {/* Recherche */}
        <div style={{ position: 'relative' }}>
          <div style={{
            position: 'relative',
            width: '300px'
          }}>
            <FaSearch style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#9ca3af',
              fontSize: '0.9rem',
              zIndex: 1
            }} />
            <input
              id="navbar-search"
              type="text"
              placeholder="Rechercher... (Ctrl+K)"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              style={{
                width: '100%',
                padding: '0.6rem 0.75rem 0.6rem 2.5rem',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                fontSize: '0.875rem',
                outline: 'none',
                transition: 'all 0.2s ease',
                backgroundColor: '#f9fafb'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#3b82f6';
                e.target.style.backgroundColor = '#ffffff';
                e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e5e7eb';
                e.target.style.backgroundColor = '#f9fafb';
                e.target.style.boxShadow = 'none';
              }}
            />
          </div>

          {/* Résultats de recherche */}
          {showSearchResults && searchQuery.length > 2 && (
            <div style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              marginTop: '0.5rem',
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
              zIndex: 1001,
              maxHeight: '300px',
              overflowY: 'auto'
            }}>
              <div style={{ padding: '0.75rem', borderBottom: '1px solid #e5e7eb', fontWeight: '600', color: '#374151' }}>
                Résultats pour "{searchQuery}"
              </div>
              <div style={{ padding: '0.5rem' }}>
                <div style={{ padding: '0.75rem', color: '#6b7280', fontSize: '0.875rem' }}>
                  Aucun résultat trouvé
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Alertes */}
        <div style={{ position: 'relative' }}>
          <button
            onClick={() => setShowAlerts(!showAlerts)}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '0.6rem',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'background-color 0.2s',
              position: 'relative',
              backgroundColor: hasUrgentAlerts ? '#fef2f2' : 'transparent'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = hasUrgentAlerts ? '#fecaca' : '#f3f4f6';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = hasUrgentAlerts ? '#fef2f2' : 'transparent';
            }}
          >
            <FaExclamationTriangle style={{
              fontSize: '1.1rem',
              color: hasUrgentAlerts ? '#dc2626' : '#6b7280'
            }} />
            {alerts.length > 0 && (
              <span style={{
                position: 'absolute',
                top: '4px',
                right: '4px',
                backgroundColor: hasUrgentAlerts ? '#dc2626' : '#f59e0b',
                color: 'white',
                borderRadius: '50%',
                width: '16px',
                height: '16px',
                fontSize: '0.7rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: '600'
              }}>
                {alerts.length}
              </span>
            )}
          </button>

          {/* Dropdown des alertes */}
          {showAlerts && (
            <div style={{
              position: 'absolute',
              top: '100%',
              right: 0,
              marginTop: '0.5rem',
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
              width: '320px',
              zIndex: 1001
            }}>
              <div style={{ padding: '1rem', borderBottom: '1px solid #e5e7eb', fontWeight: '600', color: '#374151' }}>
                Alertes ({alerts.length})
              </div>
              <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                {alerts.length > 0 ? alerts.map((alert) => (
                  <div key={alert.id} style={{
                    padding: '0.75rem 1rem',
                    borderBottom: '1px solid #f3f4f6',
                    display: 'flex',
                    alignItems: 'start',
                    gap: '0.75rem'
                  }}>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: alert.urgent ? '#dc2626' : '#f59e0b',
                      marginTop: '0.25rem',
                      flexShrink: 0
                    }} />
                    <div>
                      <p style={{ margin: 0, fontSize: '0.875rem', color: '#374151', lineHeight: '1.4' }}>
                        {alert.message}
                      </p>
                      <span style={{
                        fontSize: '0.75rem',
                        color: alert.urgent ? '#dc2626' : '#f59e0b',
                        fontWeight: '500',
                        textTransform: 'uppercase'
                      }}>
                        {alert.urgent ? 'Urgent' : 'Info'}
                      </span>
                    </div>
                  </div>
                )) : (
                  <div style={{ padding: '1rem', textAlign: 'center', color: '#6b7280', fontSize: '0.875rem' }}>
                    Aucune alerte
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Notifications */}
        <div style={{ position: 'relative' }}>
          <button
            onClick={() => setShowNotifications(!showNotifications)}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '0.6rem',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'background-color 0.2s',
              position: 'relative'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#f3f4f6';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
            }}
          >
            <FaBell style={{ fontSize: '1.1rem', color: '#6b7280' }} />
            {/* Badge de notification dynamique pour les formations */}
            {canViewNotifications && unreadCount > 0 && (
              <span style={{
                position: 'absolute',
                top: '4px',
                right: '4px',
                backgroundColor: '#ef4444',
                color: 'white',
                borderRadius: '50%',
                width: unreadCount > 9 ? '18px' : '16px',
                height: unreadCount > 9 ? '18px' : '16px',
                fontSize: '0.7rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: '600'
              }}>
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </button>

          {/* Notifications dropdown */}
          {showNotifications && (
            <div style={{
              position: 'absolute',
              top: '100%',
              right: 0,
              marginTop: '0.5rem',
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
              width: '300px',
              zIndex: 1001
            }}>
              {/* Header avec compteur et action "Tout marquer comme lu" */}
              <div style={{
                padding: '1rem',
                borderBottom: '1px solid #e5e7eb',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ fontWeight: '600', color: '#374151' }}>
                  Notifications {canViewNotifications && unreadCount > 0 && `(${unreadCount})`}
                </span>
                {canViewNotifications && unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    style={{
                      background: 'none',
                      border: 'none',
                      color: '#3b82f6',
                      fontSize: '0.75rem',
                      cursor: 'pointer',
                      fontWeight: '500'
                    }}
                  >
                    Tout marquer comme lu
                  </button>
                )}
              </div>

              {/* Contenu des notifications */}
              <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                {canViewNotifications ? (
                  formationNotifications.length > 0 ? (
                    formationNotifications.map((notification) => (
                      <div
                        key={notification._id}
                        onClick={() => handleNotificationClick(notification)}
                        style={{
                          padding: '0.75rem 1rem',
                          borderBottom: '1px solid #f3f4f6',
                          cursor: 'pointer',
                          backgroundColor: notification.vue_par_rh ? '#ffffff' : '#f0f9ff',
                          transition: 'background-color 0.2s',
                          display: 'flex',
                          alignItems: 'start',
                          gap: '0.75rem'
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = notification.vue_par_rh ? '#f9fafb' : '#e0f2fe';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = notification.vue_par_rh ? '#ffffff' : '#f0f9ff';
                        }}
                      >
                        {/* Icône de formation */}
                        <div style={{
                          width: '32px',
                          height: '32px',
                          borderRadius: '50%',
                          backgroundColor: '#3b82f6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0
                        }}>
                          <FaGraduationCap style={{ color: 'white', fontSize: '0.875rem' }} />
                        </div>

                        {/* Contenu de la notification */}
                        <div style={{ flex: 1, minWidth: 0 }}>
                          <div style={{
                            fontSize: '0.875rem',
                            fontWeight: notification.vue_par_rh ? '500' : '600',
                            color: '#1f2937',
                            marginBottom: '0.25rem',
                            lineHeight: '1.4'
                          }}>
                            {notification.employe_id?.prenom} {notification.employe_id?.nom} a postulé à "{notification.formation_id?.nom_formation}"
                          </div>
                          <div style={{
                            fontSize: '0.75rem',
                            color: '#6b7280',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem'
                          }}>
                            <span>
                              {new Date(notification.date_candidature).toLocaleDateString('fr-FR', {
                                day: '2-digit',
                                month: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                            {!notification.vue_par_rh && (
                              <span style={{
                                width: '6px',
                                height: '6px',
                                borderRadius: '50%',
                                backgroundColor: '#3b82f6'
                              }} />
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div style={{ padding: '2rem 1rem', textAlign: 'center' }}>
                      <FaGraduationCap style={{
                        fontSize: '2rem',
                        color: '#d1d5db',
                        marginBottom: '0.5rem'
                      }} />
                      <p style={{
                        margin: 0,
                        fontSize: '0.875rem',
                        color: '#6b7280',
                        fontWeight: '500'
                      }}>
                        Aucune nouvelle candidature
                      </p>
                      <p style={{
                        margin: '0.25rem 0 0 0',
                        fontSize: '0.75rem',
                        color: '#9ca3af'
                      }}>
                        Les candidatures des employés apparaîtront ici
                      </p>
                    </div>
                  )
                ) : (
                  <div style={{ padding: '1rem', textAlign: 'center' }}>
                    <p style={{ margin: 0, fontSize: '0.875rem', color: '#6b7280' }}>
                      Notifications disponibles pour RH et Admin uniquement
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Actions rapides */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          {/* Bouton paramètres */}
          <button
            onClick={() => {/* TODO: Ouvrir paramètres */}}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '0.6rem',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'background-color 0.2s'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#f3f4f6';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
            }}
            title="Paramètres"
          >
            <FaCog style={{ fontSize: '1.1rem', color: '#6b7280' }} />
          </button>

          {/* Bouton déconnexion */}
          <button
            onClick={handleLogout}
            style={{
              background: 'none',
              border: '1px solid #dc2626',
              cursor: 'pointer',
              padding: '0.5rem 1rem',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'all 0.2s',
              color: '#dc2626',
              fontSize: '0.875rem',
              fontWeight: '500'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#dc2626';
              e.target.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.color = '#dc2626';
            }}
            title="Déconnexion"
          >
            <FaSignOutAlt style={{ fontSize: '0.9rem' }} />
            Déconnexion
          </button>
        </div>
      </div>
    </nav>
  );
};

export default ModernNavbar;

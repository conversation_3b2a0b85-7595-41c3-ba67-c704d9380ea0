import mongoose from "mongoose";

const archiveSchema = new mongoose.Schema({
  // Type d'élément archivé
  type_element: {
    type: String,
    enum: ['Projet', 'Tache', 'Employe', 'Contrat', 'Autre'],
    required: true,
    index: true
  },
  
  // ID de l'élément original
  element_id: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    index: true
  },
  
  // Données complètes de l'élément au moment de l'archivage
  donnees_originales: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  
  // Informations sur l'archivage
  raison_archivage: {
    type: String,
    enum: [
      'Projet terminé',
      'Projet annulé', 
      'Employé parti',
      'Contrat expiré',
      'Obsolète',
      'Demande utilisateur',
      'Maintenance',
      'Autre'
    ],
    required: true
  },
  
  description_archivage: {
    type: String,
    trim: true,
    maxlength: 500
  },
  
  // Qui a archivé
  archive_par: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employe",
    required: true,
    index: true
  },
  
  // Dates importantes
  date_archivage: {
    type: Date,
    default: Date.now,
    required: true,
    index: true
  },
  
  date_element_original: {
    type: Date,
    required: true
  },
  
  // Métadonnées pour la recherche
  tags: [{
    type: String,
    trim: true
  }],
  
  // Informations de restauration
  peut_etre_restaure: {
    type: Boolean,
    default: true
  },
  
  date_suppression_prevue: {
    type: Date,
    index: true
  },
  
  // Statut de l'archive
  statut_archive: {
    type: String,
    enum: ['Archivé', 'Suppression programmée', 'Supprimé définitivement'],
    default: 'Archivé',
    index: true
  },
  
  // Informations de restauration si applicable
  restauration: {
    date_restauration: Date,
    restaure_par: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employe"
    },
    raison_restauration: String,
    nouvel_id: mongoose.Schema.Types.ObjectId
  },
  
  // Taille des données (pour gestion de l'espace)
  taille_donnees: {
    type: Number,
    default: 0
  },
  
  // Références liées (pour projets avec tâches par exemple)
  elements_lies: [{
    type: mongoose.Schema.Types.ObjectId,
    element_type: String
  }],
  
  // Niveau de priorité pour la conservation
  priorite_conservation: {
    type: String,
    enum: ['Faible', 'Normale', 'Haute', 'Critique'],
    default: 'Normale'
  },
  
  // Informations de conformité/légal
  duree_conservation_legale: {
    type: Number, // en années
    default: 5
  },
  
  conforme_rgpd: {
    type: Boolean,
    default: true
  },
  
  // Commentaires et notes
  commentaires: [{
    auteur: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employe"
    },
    contenu: {
      type: String,
      maxlength: 300
    },
    date: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Index composés pour optimiser les requêtes
archiveSchema.index({ type_element: 1, date_archivage: -1 });
archiveSchema.index({ archive_par: 1, date_archivage: -1 });
archiveSchema.index({ statut_archive: 1, date_suppression_prevue: 1 });
archiveSchema.index({ element_id: 1, type_element: 1 }, { unique: true });

// Méthodes d'instance
archiveSchema.methods.peutEtreRestaure = function() {
  return this.peut_etre_restaure &&
         this.statut_archive === 'Archivé' &&
         !this.restauration.date_restauration;
};

archiveSchema.methods.estExpire = function() {
  if (!this.date_suppression_prevue) return false;
  return new Date() > this.date_suppression_prevue;
};

// Alias pour compatibilité avec l'ancien système
archiveSchema.methods.isExpired = function() {
  return this.estExpire();
};

archiveSchema.methods.joursAvantSuppression = function() {
  if (!this.date_suppression_prevue) return null;

  const maintenant = new Date();
  const diffTime = this.date_suppression_prevue - maintenant;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return Math.max(0, diffDays);
};

// Alias pour compatibilité avec l'ancien système
archiveSchema.methods.getTimeUntilExpiration = function() {
  return this.joursAvantSuppression();
};

// Méthode pour calculer l'âge de l'archive (depuis l'ancien système)
archiveSchema.methods.getAge = function() {
  const now = new Date();
  const diffTime = Math.abs(now - this.date_archivage);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

archiveSchema.methods.calculerTailleDonnees = function() {
  const taille = JSON.stringify(this.donnees_originales).length;
  this.taille_donnees = taille;
  return taille;
};

archiveSchema.methods.ajouterCommentaire = function(auteur, contenu) {
  this.commentaires.push({
    auteur,
    contenu,
    date: new Date()
  });
};

// Méthodes statiques
archiveSchema.statics.getStatistiques = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$type_element',
        count: { $sum: 1 },
        tailleTotal: { $sum: '$taille_donnees' }
      }
    }
  ]);

  const total = await this.countDocuments();
  const restaurables = await this.countDocuments({
    peut_etre_restaure: true,
    statut_archive: 'Archivé'
  });

  const expiresProchainement = await this.countDocuments({
    date_suppression_prevue: {
      $lte: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 jours
    },
    statut_archive: 'Archivé'
  });

  return {
    total,
    restaurables,
    expiresProchainement,
    parType: stats
  };
};

// Alias pour compatibilité avec l'ancien système
archiveSchema.statics.getStats = async function() {
  const stats = await this.getStatistiques();

  // Adapter le format pour l'ancien système
  const raisonsCount = {};
  const archives = await this.find({ statut_archive: 'Archivé' });

  archives.forEach(archive => {
    const raison = archive.raison_archivage;
    raisonsCount[raison] = (raisonsCount[raison] || 0) + 1;
  });

  return {
    total: stats.total,
    totalTaches: archives.filter(a => a.type_element === 'Tache').length,
    tailleTotale: stats.parType.reduce((sum, type) => sum + type.tailleTotal, 0),
    raisonsCount
  };
};

archiveSchema.statics.nettoyageAutomatique = async function() {
  // Marquer pour suppression les archives expirées
  const maintenant = new Date();

  const result = await this.updateMany(
    {
      date_suppression_prevue: { $lte: maintenant },
      statut_archive: 'Archivé'
    },
    {
      statut_archive: 'Suppression programmée'
    }
  );

  return result;
};

// Alias pour compatibilité avec l'ancien système
archiveSchema.statics.cleanupExpiredArchives = async function() {
  const maintenant = new Date();

  // Trouver les archives expirées
  const expiredArchives = await this.find({
    date_suppression_prevue: { $lte: maintenant },
    statut_archive: 'Archivé'
  });

  // Marquer comme supprimées au lieu de supprimer définitivement
  await this.updateMany(
    {
      date_suppression_prevue: { $lte: maintenant },
      statut_archive: 'Archivé'
    },
    {
      statut_archive: 'Supprimé définitivement',
      'restauration.date_restauration': maintenant
    }
  );

  return expiredArchives.length;
};

archiveSchema.statics.restaurerElement = async function(archiveId, utilisateur) {
  const archive = await this.findById(archiveId);
  
  if (!archive || !archive.peutEtreRestaure()) {
    throw new Error('Cet élément ne peut pas être restauré');
  }
  
  // Marquer comme restauré
  archive.restauration = {
    date_restauration: new Date(),
    restaure_par: utilisateur,
    raison_restauration: 'Restauration manuelle'
  };
  
  await archive.save();
  
  return archive.donnees_originales;
};

// Middleware pre-save
archiveSchema.pre('save', function(next) {
  // Calculer automatiquement la taille des données
  if (this.isModified('donnees_originales')) {
    this.calculerTailleDonnees();
  }
  
  // Définir une date de suppression par défaut si pas définie
  if (!this.date_suppression_prevue && this.duree_conservation_legale) {
    const dateSuppr = new Date(this.date_archivage);
    dateSuppr.setFullYear(dateSuppr.getFullYear() + this.duree_conservation_legale);
    this.date_suppression_prevue = dateSuppr;
  }
  
  next();
});

const Archive = mongoose.model("Archive", archiveSchema);
export default Archive;

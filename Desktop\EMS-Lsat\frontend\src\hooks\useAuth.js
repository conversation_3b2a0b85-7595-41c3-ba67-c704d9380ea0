/**
 * Hook personnalisé pour la gestion de l'authentification
 */
import { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { authService } from '../services/authService';

export const useAuth = () => {
    const context = useContext(AuthContext);
    
    if (!context) {
        throw new Error('useAuth doit être utilisé dans un AuthProvider');
    }
    
    return context;
};

/**
 * Hook pour vérifier l'authentification
 */
export const useAuthCheck = () => {
    const [isLoading, setIsLoading] = useState(true);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [user, setUser] = useState(null);

    useEffect(() => {
        const checkAuth = async () => {
            try {
                const token = authService.getToken();
                const currentUser = authService.getCurrentUser();
                
                if (token && currentUser) {
                    // Vérifier la validité du token
                    await authService.verifyToken();
                    setIsAuthenticated(true);
                    setUser(currentUser);
                } else {
                    setIsAuthenticated(false);
                    setUser(null);
                }
            } catch (error) {
                console.error('Erreur de vérification d\'authentification:', error);
                setIsAuthenticated(false);
                setUser(null);
                authService.logout();
            } finally {
                setIsLoading(false);
            }
        };

        checkAuth();
    }, []);

    return { isLoading, isAuthenticated, user };
};

/**
 * Hook pour la connexion
 */
export const useLogin = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);

    const login = async (credentials) => {
        setIsLoading(true);
        setError(null);
        
        try {
            const result = await authService.login(credentials);
            setIsLoading(false);
            return result;
        } catch (error) {
            setError(error.message || 'Erreur de connexion');
            setIsLoading(false);
            throw error;
        }
    };

    return { login, isLoading, error };
};

/**
 * Hook pour vérifier les permissions
 */
export const usePermissions = () => {
    const { user } = useAuth();

    const hasPermission = (permission) => {
        return authService.hasPermission(permission);
    };

    const hasRole = (role) => {
        return authService.hasRole(role);
    };

    const hasAnyPermission = (permissions) => {
        return permissions.some(permission => hasPermission(permission));
    };

    const hasAllPermissions = (permissions) => {
        return permissions.every(permission => hasPermission(permission));
    };

    return {
        user,
        hasPermission,
        hasRole,
        hasAnyPermission,
        hasAllPermissions
    };
};

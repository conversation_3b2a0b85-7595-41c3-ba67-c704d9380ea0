/**
 * Service pour la gestion des employés
 */
import api from './api';

export const employeeService = {
    /**
     * Obtenir tous les employés
     * @returns {Promise} Liste des employés
     */
    getAllEmployees: async () => {
        try {
            const response = await api.get('/employees');
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des employés' };
        }
    },

    /**
     * Obtenir un employé par ID
     * @param {string} id - ID de l'employé
     * @returns {Promise} Données de l'employé
     */
    getEmployeeById: async (id) => {
        try {
            const response = await api.get(`/employees/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération de l\'employé' };
        }
    },

    /**
     * Créer un nouvel employé
     * @param {FormData} employeeData - Données de l'employé (avec photo)
     * @returns {Promise} Employé cré<PERSON>
     */
    createEmployee: async (employeeData) => {
        try {
            const response = await api.post('/employees', employeeData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la création de l\'employé' };
        }
    },

    /**
     * Mettre à jour un employé
     * @param {string} id - ID de l'employé
     * @param {FormData} employeeData - Nouvelles données de l'employé
     * @returns {Promise} Employé mis à jour
     */
    updateEmployee: async (id, employeeData) => {
        try {
            const response = await api.put(`/employees/${id}`, employeeData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la mise à jour de l\'employé' };
        }
    },

    /**
     * Supprimer un employé
     * @param {string} id - ID de l'employé
     * @returns {Promise} Confirmation de suppression
     */
    deleteEmployee: async (id) => {
        try {
            const response = await api.delete(`/employees/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la suppression de l\'employé' };
        }
    },

    /**
     * Obtenir les statistiques des employés
     * @returns {Promise} Statistiques
     */
    getEmployeeStats: async () => {
        try {
            const response = await api.get('/employees/stats');
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des statistiques' };
        }
    },

    /**
     * Obtenir les informations de l'employé connecté
     * @returns {Promise} Informations de l'employé
     */
    getMyInfo: async () => {
        try {
            const response = await api.get('/employees/my-info');
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération de vos informations' };
        }
    },

    /**
     * Rechercher des employés
     * @param {string} query - Terme de recherche
     * @returns {Promise} Employés trouvés
     */
    searchEmployees: async (query) => {
        try {
            const response = await api.get(`/employees/search?q=${encodeURIComponent(query)}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la recherche' };
        }
    }
};

// Importing required modules
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/authContext.jsx'; // Assurez-vous d'importer le contexte d'authentification

const RoleBaseRoutes = ({children , requiredRole}) =>{
    const { utilisateur, loading } = useAuth();

    if (loading){
      return  <div>Loading ....</div>
    }

    // Gérer le cas où requiredRole est une chaîne ou un tableau
    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];

    if (!allowedRoles.includes(utilisateur.role)) {
        console.log("Unauthorized access");
        return <Navigate to="/Login" />;
    }

    return utilisateur ? children : <Navigate to="/login" />;
}

export default RoleBaseRoutes
/**
 * Service pour la gestion des services
 */
import api from './api';

export const serviceService = {
    /**
     * Obtenir tous les services
     * @param {Object} params - Paramètres de pagination et filtres
     * @returns {Promise} Liste des services
     */
    getAllServices: async (params = {}) => {
        try {
            const { page = 1, limit = 10, departement } = params;
            let url = `/services?page=${page}&limit=${limit}`;
            if (departement) {
                url += `&departement=${departement}`;
            }
            const response = await api.get(url);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des services' };
        }
    },

    /**
     * Obtenir un service par ID
     * @param {string} id - ID du service
     * @returns {Promise} Données du service
     */
    getServiceById: async (id) => {
        try {
            const response = await api.get(`/services/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération du service' };
        }
    },

    /**
     * Créer un nouveau service
     * @param {Object} serviceData - Données du service
     * @returns {Promise} Service créé
     */
    createService: async (serviceData) => {
        try {
            const response = await api.post('/services', serviceData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la création du service' };
        }
    },

    /**
     * Mettre à jour un service
     * @param {string} id - ID du service
     * @param {Object} serviceData - Nouvelles données du service
     * @returns {Promise} Service mis à jour
     */
    updateService: async (id, serviceData) => {
        try {
            const response = await api.put(`/services/${id}`, serviceData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la mise à jour du service' };
        }
    },

    /**
     * Supprimer un service
     * @param {string} id - ID du service
     * @returns {Promise} Confirmation de suppression
     */
    deleteService: async (id) => {
        try {
            const response = await api.delete(`/services/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la suppression du service' };
        }
    },

    /**
     * Obtenir les statistiques des services
     * @returns {Promise} Statistiques
     */
    getServiceStats: async () => {
        try {
            const response = await api.get('/services/stats');
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des statistiques' };
        }
    },

    /**
     * Obtenir les services d'un département spécifique
     * @param {string} departmentId - ID du département
     * @returns {Promise} Services du département
     */
    getServicesByDepartment: async (departmentId) => {
        try {
            const response = await api.get(`/services/department/${departmentId}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des services' };
        }
    }
};

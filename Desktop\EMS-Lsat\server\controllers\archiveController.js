/**
 * Contrôleur pour la gestion des archives
 */
import Archive from '../models/Archive.js';
import Projet from '../models/Projet.js';
import Tache from '../models/Tache.js';

/**
 * Obtenir toutes les archives
 */
export const getArchives = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            raison,
            search,
            sortBy = 'date_archivage',
            sortOrder = 'desc'
        } = req.query;

        // Construire le filtre pour les projets archivés
        const filter = {
            type_element: 'Projet',
            statut_archive: 'Archivé'
        };

        if (raison) {
            filter.raison_archivage = raison;
        }

        if (search) {
            filter.$or = [
                { 'donnees_originales.nom_projet': { $regex: search, $options: 'i' } },
                { 'donnees_originales.description': { $regex: search, $options: 'i' } },
                { 'donnees_originales.client': { $regex: search, $options: 'i' } },
                { description_archivage: { $regex: search, $options: 'i' } }
            ];
        }

        // Options de tri
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Exécuter la requête
        const archives = await Archive.find(filter)
            .populate('archive_par', 'prenom nom email')
            .populate('restauration.restaure_par', 'prenom nom email')
            .sort(sortOptions)
            .skip(skip)
            .limit(parseInt(limit));

        // Compter le total
        const total = await Archive.countDocuments(filter);

        res.status(200).json({
            success: true,
            data: {
                archives,
                pagination: {
                    current: parseInt(page),
                    pages: Math.ceil(total / parseInt(limit)),
                    total,
                    limit: parseInt(limit)
                }
            }
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des archives:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur lors de la récupération des archives'
        });
    }
};

/**
 * Obtenir une archive par ID
 */
export const getArchiveById = async (req, res) => {
    try {
        const { id } = req.params;

        const archive = await Archive.findById(id)
            .populate('archive_par', 'prenom nom email')
            .populate('restaure_par', 'prenom nom email');

        if (!archive) {
            return res.status(404).json({
                success: false,
                message: 'Archive non trouvée'
            });
        }

        res.status(200).json({
            success: true,
            data: { archive }
        });
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'archive:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur lors de la récupération de l\'archive'
        });
    }
};

/**
 * Archiver un projet
 */
export const archiverProjet = async (req, res) => {
    try {
        console.log('🔄 Début archivage projet:', req.params.id);
        console.log('📝 Données reçues:', req.body);
        console.log('👤 Utilisateur:', req.user || req.utilisateur);

        const { id: projetId } = req.params;
        const { raison_archivage, description_archivage } = req.body;
        const userId = req.user?._id || req.utilisateur?._id;

        if (!userId) {
            console.error('❌ Utilisateur non trouvé dans req.user ou req.utilisateur');
            return res.status(401).json({
                success: false,
                message: 'Utilisateur non authentifié'
            });
        }

        console.log('🔍 Recherche du projet:', projetId);

        // Vérifier que le projet existe
        const projet = await Projet.findById(projetId)
            .populate('chef_projet', 'prenom nom email')
            .populate('departement', 'nom_departement');

        console.log('📋 Projet trouvé:', projet ? 'Oui' : 'Non');

        if (!projet) {
            return res.status(404).json({
                success: false,
                message: 'Projet non trouvé'
            });
        }

        console.log('🔍 Recherche des tâches du projet...');

        // Récupérer toutes les tâches du projet
        const taches = await Tache.find({ projet: projetId })
            .populate('employe_assigne', 'prenom nom email');

        console.log('📋 Tâches trouvées:', taches.length);

        // Créer l'objet projet complet pour l'archive
        const projetComplet = {
            ...projet.toObject(),
            taches: taches
        };

        console.log('📦 Création de l\'archive...');

        // Créer l'archive avec le modèle existant
        const archive = new Archive({
            type_element: 'Projet',
            element_id: projetId,
            donnees_originales: projetComplet,
            raison_archivage,
            description_archivage: description_archivage || '',
            archive_par: userId,
            date_element_original: projet.date_debut || projet.createdAt,
            peut_etre_restaure: true,
            priorite_conservation: 'Normale',
            duree_conservation_legale: 5
        });

        console.log('💾 Sauvegarde de l\'archive...');
        await archive.save();
        console.log('✅ Archive sauvegardée avec succès');

        // Supprimer le projet et ses tâches de la base active
        await Tache.deleteMany({ projet: projetId });
        await Projet.findByIdAndDelete(projetId);

        res.status(201).json({
            success: true,
            message: 'Projet archivé avec succès',
            data: { archive }
        });
    } catch (error) {
        console.error('❌ Erreur lors de l\'archivage du projet:', error);
        console.error('❌ Stack trace:', error.stack);
        res.status(500).json({
            success: false,
            message: 'Erreur lors de l\'archivage du projet',
            error: error.message
        });
    }
};

/**
 * Restaurer un projet archivé
 */
export const restaurerProjet = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user._id || req.utilisateur._id;

        // Trouver l'archive
        const archive = await Archive.findById(id);

        if (!archive) {
            return res.status(404).json({
                success: false,
                message: 'Archive non trouvée'
            });
        }

        if (!archive.peutEtreRestaure()) {
            return res.status(400).json({
                success: false,
                message: 'Cette archive ne peut pas être restaurée'
            });
        }

        // Vérifier qu'un projet avec le même nom n'existe pas déjà
        const existingProjet = await Projet.findOne({
            nom_projet: archive.donnees_originales.nom_projet
        });

        if (existingProjet) {
            return res.status(400).json({
                success: false,
                message: 'Un projet avec ce nom existe déjà'
            });
        }

        // Restaurer le projet
        const projetData = { ...archive.donnees_originales };
        delete projetData._id;
        delete projetData.taches;

        const nouveauProjet = new Projet(projetData);
        await nouveauProjet.save();

        // Restaurer les tâches
        if (archive.donnees_originales.taches && archive.donnees_originales.taches.length > 0) {
            const tachesData = archive.donnees_originales.taches.map(tache => {
                const tacheData = { ...tache };
                delete tacheData._id;
                tacheData.projet = nouveauProjet._id;
                return tacheData;
            });

            await Tache.insertMany(tachesData);
        }

        // Mettre à jour l'archive
        archive.restauration = {
            date_restauration: new Date(),
            restaure_par: userId,
            raison_restauration: 'Restauration manuelle',
            nouvel_id: nouveauProjet._id
        };
        await archive.save();

        res.status(200).json({
            success: true,
            message: 'Projet restauré avec succès',
            data: { projet: nouveauProjet }
        });
    } catch (error) {
        console.error('Erreur lors de la restauration du projet:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur lors de la restauration du projet'
        });
    }
};

/**
 * Supprimer définitivement une archive
 */
export const supprimerArchive = async (req, res) => {
    try {
        const { id } = req.params;

        const archive = await Archive.findById(id);

        if (!archive) {
            return res.status(404).json({
                success: false,
                message: 'Archive non trouvée'
            });
        }

        // Supprimer définitivement
        await Archive.findByIdAndDelete(id);

        res.status(200).json({
            success: true,
            message: 'Archive supprimée définitivement'
        });
    } catch (error) {
        console.error('Erreur lors de la suppression de l\'archive:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur lors de la suppression de l\'archive'
        });
    }
};

/**
 * Obtenir les statistiques des archives
 */
export const getArchiveStats = async (req, res) => {
    try {
        // Statistiques générales pour les projets
        const stats = await Archive.aggregate([
            { $match: { type_element: 'Projet', statut_archive: 'Archivé' } },
            {
                $group: {
                    _id: null,
                    total: { $sum: 1 },
                    tailleTotale: { $sum: '$taille_donnees' }
                }
            }
        ]);

        // Statistiques par raison d'archivage
        const raisonsStats = await Archive.aggregate([
            { $match: { type_element: 'Projet', statut_archive: 'Archivé' } },
            {
                $group: {
                    _id: '$raison_archivage',
                    count: { $sum: 1 }
                }
            },
            { $sort: { count: -1 } }
        ]);

        // Archives récentes
        const archivesRecentes = await Archive.find({
            type_element: 'Projet',
            statut_archive: 'Archivé'
        })
            .sort({ date_archivage: -1 })
            .limit(5)
            .select('donnees_originales.nom_projet raison_archivage date_archivage')
            .populate('archive_par', 'prenom nom');

        const result = {
            total: stats.length > 0 ? stats[0].total : 0,
            tailleTotale: stats.length > 0 ? stats[0].tailleTotale : 0,
            raisonsStats,
            archivesRecentes
        };

        res.status(200).json({
            success: true,
            data: result
        });
    } catch (error) {
        console.error('Erreur lors de la récupération des statistiques:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur lors de la récupération des statistiques'
        });
    }
};

/**
 * Nettoyer les archives expirées
 */
export const nettoyerArchivesExpirees = async (req, res) => {
    try {
        const result = await Archive.nettoyageAutomatique();

        res.status(200).json({
            success: true,
            message: `${result.modifiedCount} archive(s) expirée(s) nettoyée(s)`,
            data: { nombreSupprimees: result.modifiedCount }
        });
    } catch (error) {
        console.error('Erreur lors du nettoyage des archives:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur lors du nettoyage des archives'
        });
    }
};

import mongoose from "mongoose";

const contratSchema = new mongoose.Schema({
  type_contrat: {
    type: String,
    required: true,
    enum: ['CDI', 'CDD', 'Stage', 'Freelance', 'Apprentissage', 'Interim'],
    index: true
  },
  date_debut: {
    type: Date,
    required: true,
    index: true
  },
  date_fin: {
    type: Date,
    validate: {
      validator: function(value) {
        // date_fin optionnelle pour CDI, obligatoire pour CDD, Stage, etc.
        if (this.type_contrat === 'CDI') return true;
        return value && value > this.date_debut;
      },
      message: 'La date de fin doit être postérieure à la date de début'
    }
  },
  salaire: {
    type: Number,
    required: true,
    min: 0
  },
  employe: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Employe",
    required: true,
    index: true
  },
  statut: {
    type: String,
    enum: ['Actif', 'Terminé', 'Suspendu', 'Annulé'],
    default: 'Actif',
    index: true
  },
  description: {
    type: String,
    trim: true
  },
  conditions_particulieres: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Index composé pour éviter les doublons employe + type_contrat actif
contratSchema.index({ employe: 1, statut: 1 });

// Méthode pour vérifier si le contrat est actif
contratSchema.methods.isActif = function() {
  const now = new Date();
  return this.statut === 'Actif' &&
         this.date_debut <= now &&
         (!this.date_fin || this.date_fin >= now);
};

// Méthode pour calculer la durée du contrat
contratSchema.methods.getDuree = function() {
  if (!this.date_fin) return null; // CDI
  const diffTime = Math.abs(this.date_fin - this.date_debut);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

const Contrat = mongoose.model("Contrat", contratSchema);
export default Contrat;

import React from 'react';
import { 
  FaFacebookF, 
  <PERSON>a<PERSON><PERSON><PERSON>, 
  FaLinkedinIn, 
  FaInstagram,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaHeart,
  FaShieldAlt,
  FaUsers,
  FaBuilding
} from 'react-icons/fa';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer style={{
      background: 'linear-gradient(135deg, #1e293b 0%, #334155 50%, #1e293b 100%)',
      color: '#f8fafc',
      marginTop: 'auto',
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
    }}>
      {/* Section principale */}
      <div style={{
        padding: '3rem 2rem 2rem 2rem',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '2.5rem',
          marginBottom: '2rem'
        }}>
          
          {/* Section À propos */}
          <div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <div style={{
                width: '3rem',
                height: '3rem',
                background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '1rem'
              }}>
                <FaBuilding style={{ color: 'white', fontSize: '1.2rem' }} />
              </div>
              <h3 style={{
                margin: 0,
                fontSize: '1.5rem',
                fontWeight: '700',
                background: 'linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}>
                EMS Platform
              </h3>
            </div>
            <p style={{
              margin: '0 0 1.5rem 0',
              lineHeight: '1.6',
              color: '#cbd5e1',
              fontSize: '0.95rem'
            }}>
              Système de Gestion des Employés moderne et intuitif. 
              Optimisez la gestion de vos ressources humaines avec notre plateforme complète.
            </p>
            <div style={{
              display: 'flex',
              gap: '1rem'
            }}>
              {[
                { icon: FaFacebookF, color: '#1877f2' },
                { icon: FaTwitter, color: '#1da1f2' },
                { icon: FaLinkedinIn, color: '#0077b5' },
                { icon: FaInstagram, color: '#e4405f' }
              ].map((social, index) => (
                <a
                  key={index}
                  href="#"
                  style={{
                    width: '2.5rem',
                    height: '2.5rem',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#cbd5e1',
                    transition: 'all 0.3s ease',
                    textDecoration: 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = social.color;
                    e.target.style.color = 'white';
                    e.target.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    e.target.style.color = '#cbd5e1';
                    e.target.style.transform = 'translateY(0)';
                  }}
                >
                  <social.icon />
                </a>
              ))}
            </div>
          </div>

          {/* Section Liens rapides */}
          <div>
            <h4 style={{
              margin: '0 0 1.5rem 0',
              fontSize: '1.2rem',
              fontWeight: '600',
              color: '#f1f5f9'
            }}>
              Liens Rapides
            </h4>
            <ul style={{
              listStyle: 'none',
              padding: 0,
              margin: 0
            }}>
              {[
                'Tableau de bord',
                'Gestion des employés',
                'Départements',
                'Projets',
                'Formations',
                'Rapports'
              ].map((link, index) => (
                <li key={index} style={{ marginBottom: '0.75rem' }}>
                  <a
                    href="#"
                    style={{
                      color: '#cbd5e1',
                      textDecoration: 'none',
                      fontSize: '0.95rem',
                      transition: 'color 0.3s ease',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.color = '#60a5fa';
                      e.target.style.paddingLeft = '0.5rem';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.color = '#cbd5e1';
                      e.target.style.paddingLeft = '0';
                    }}
                  >
                    {link}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Section Contact */}
          <div>
            <h4 style={{
              margin: '0 0 1.5rem 0',
              fontSize: '1.2rem',
              fontWeight: '600',
              color: '#f1f5f9'
            }}>
              Contact
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              {[
                { icon: FaMapMarkerAlt, text: '123 Rue de l\'Innovation, 75001 Paris' },
                { icon: FaPhone, text: '+33 1 23 45 67 89' },
                { icon: FaEnvelope, text: '<EMAIL>' }
              ].map((contact, index) => (
                <div key={index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem'
                }}>
                  <div style={{
                    width: '2rem',
                    height: '2rem',
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    borderRadius: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#60a5fa'
                  }}>
                    <contact.icon style={{ fontSize: '0.9rem' }} />
                  </div>
                  <span style={{
                    color: '#cbd5e1',
                    fontSize: '0.95rem'
                  }}>
                    {contact.text}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Section Sécurité & Conformité */}
          <div>
            <h4 style={{
              margin: '0 0 1.5rem 0',
              fontSize: '1.2rem',
              fontWeight: '600',
              color: '#f1f5f9'
            }}>
              Sécurité & Conformité
            </h4>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem'
              }}>
                <FaShieldAlt style={{ color: '#10b981', fontSize: '1.2rem' }} />
                <span style={{ color: '#cbd5e1', fontSize: '0.95rem' }}>
                  Données sécurisées SSL
                </span>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem'
              }}>
                <FaUsers style={{ color: '#8b5cf6', fontSize: '1.2rem' }} />
                <span style={{ color: '#cbd5e1', fontSize: '0.95rem' }}>
                  Conforme RGPD
                </span>
              </div>
              <div style={{
                padding: '1rem',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderRadius: '8px',
                border: '1px solid rgba(59, 130, 246, 0.2)'
              }}>
                <p style={{
                  margin: 0,
                  fontSize: '0.85rem',
                  color: '#cbd5e1',
                  lineHeight: '1.5'
                }}>
                  <strong style={{ color: '#60a5fa' }}>Certification ISO 27001</strong><br />
                  Sécurité des données garantie
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section copyright */}
      <div style={{
        borderTop: '1px solid rgba(255, 255, 255, 0.1)',
        padding: '1.5rem 2rem',
        backgroundColor: 'rgba(0, 0, 0, 0.2)'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            color: '#94a3b8',
            fontSize: '0.9rem'
          }}>
            <span>© {currentYear} EMS Platform. Tous droits réservés.</span>
            <span>Fait avec</span>
            <FaHeart style={{ color: '#ef4444', fontSize: '0.8rem' }} />
            <span>par l'équipe EMS</span>
          </div>
          <div style={{
            display: 'flex',
            gap: '2rem',
            fontSize: '0.9rem'
          }}>
            {['Politique de confidentialité', 'Conditions d\'utilisation', 'Support'].map((link, index) => (
              <a
                key={index}
                href="#"
                style={{
                  color: '#94a3b8',
                  textDecoration: 'none',
                  transition: 'color 0.3s ease'
                }}
                onMouseEnter={(e) => e.target.style.color = '#60a5fa'}
                onMouseLeave={(e) => e.target.style.color = '#94a3b8'}
              >
                {link}
              </a>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

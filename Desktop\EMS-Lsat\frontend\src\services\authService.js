/**
 * Service pour la gestion de l'authentification
 */
import api from './api';

export const authService = {
    /**
     * Connexion utilisateur
     * @param {Object} credentials - Email et mot de passe
     * @returns {Promise} Réponse de l'API
     */
    login: async (credentials) => {
        try {
            const response = await api.post('/auth/login', credentials);
            
            if (response.data.success && response.data.token) {
                // Stocker le token et les informations utilisateur
                localStorage.setItem('token', response.data.token);
                localStorage.setItem('user', JSON.stringify(response.data.user));
            }
            
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur de connexion' };
        }
    },

    /**
     * Déconnexion utilisateur
     */
    logout: () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/login';
    },

    /**
     * Vérification du token
     * @returns {Promise} Réponse de l'API
     */
    verifyToken: async () => {
        try {
            const response = await api.get('/auth/verify');
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Token invalide' };
        }
    },

    /**
     * Obtenir l'utilisateur actuel depuis le localStorage
     * @returns {Object|null} Utilisateur ou null
     */
    getCurrentUser: () => {
        try {
            const user = localStorage.getItem('user');
            return user ? JSON.parse(user) : null;
        } catch (error) {
            console.error('Erreur lors de la récupération de l\'utilisateur:', error);
            return null;
        }
    },

    /**
     * Vérifier si l'utilisateur est connecté
     * @returns {boolean} True si connecté
     */
    isAuthenticated: () => {
        const token = localStorage.getItem('token');
        const user = authService.getCurrentUser();
        return !!(token && user);
    },

    /**
     * Obtenir le token d'authentification
     * @returns {string|null} Token ou null
     */
    getToken: () => {
        return localStorage.getItem('token');
    },

    /**
     * Vérifier si l'utilisateur a un rôle spécifique
     * @param {string} role - Rôle à vérifier
     * @returns {boolean} True si l'utilisateur a le rôle
     */
    hasRole: (role) => {
        const user = authService.getCurrentUser();
        return user?.role === role;
    },

    /**
     * Vérifier si l'utilisateur a une permission spécifique
     * @param {string} permission - Permission à vérifier
     * @returns {boolean} True si l'utilisateur a la permission
     */
    hasPermission: (permission) => {
        const user = authService.getCurrentUser();
        return user?.permissions?.includes(permission) || false;
    }
};

/**
 * Définitions de types pour l'application EMS
 * (Utilisé comme documentation et validation)
 */

/**
 * @typedef {Object} User
 * @property {string} _id - ID de l'utilisateur
 * @property {string} email - Email de l'utilisateur
 * @property {string} role - <PERSON><PERSON><PERSON> de l'utilisateur (admin, RH, employee)
 * @property {string[]} permissions - Permissions de l'utilisateur
 * @property {boolean} actif - Statut actif de l'utilisateur
 * @property {Date} createdAt - Date de création
 * @property {Date} updatedAt - Date de mise à jour
 */

/**
 * @typedef {Object} Employee
 * @property {string} _id - ID de l'employé
 * @property {string} nom - Nom de famille
 * @property {string} prenom - Prénom
 * @property {string} email - Email
 * @property {string} telephone - Numéro de téléphone
 * @property {Date} date_naissance - Date de naissance
 * @property {string} adresse - Adresse
 * @property {string} poste - Poste occupé
 * @property {number} salaire - Salaire
 * @property {Date} date_embauche - Date d'embauche
 * @property {string} photo - URL de la photo
 * @property {string} utilisateur - ID de l'utilisateur associé
 * @property {string} departement - ID du département
 * @property {Date} createdAt - Date de création
 * @property {Date} updatedAt - Date de mise à jour
 */

/**
 * @typedef {Object} Department
 * @property {string} _id - ID du département
 * @property {string} nom - Nom du département
 * @property {string} description - Description du département
 * @property {string} chef_departement - ID du chef de département
 * @property {Date} createdAt - Date de création
 * @property {Date} updatedAt - Date de mise à jour
 */

/**
 * @typedef {Object} ApiResponse
 * @property {boolean} success - Statut de la réponse
 * @property {string} message - Message de la réponse
 * @property {*} data - Données de la réponse
 * @property {string} timestamp - Timestamp de la réponse
 */

/**
 * @typedef {Object} LoginCredentials
 * @property {string} email - Email de connexion
 * @property {string} password - Mot de passe
 */

/**
 * @typedef {Object} AuthState
 * @property {boolean} isAuthenticated - Statut d'authentification
 * @property {User|null} user - Utilisateur connecté
 * @property {string|null} token - Token d'authentification
 * @property {boolean} loading - État de chargement
 */

/**
 * @typedef {Object} EmployeeFormData
 * @property {string} nom - Nom de famille
 * @property {string} prenom - Prénom
 * @property {string} email - Email
 * @property {string} telephone - Numéro de téléphone
 * @property {string} date_naissance - Date de naissance
 * @property {string} adresse - Adresse
 * @property {string} poste - Poste occupé
 * @property {number} salaire - Salaire
 * @property {string} date_embauche - Date d'embauche
 * @property {File|null} photo - Fichier photo
 * @property {string} departement - ID du département
 */

/**
 * @typedef {Object} ValidationError
 * @property {string} field - Champ en erreur
 * @property {string} message - Message d'erreur
 */

/**
 * @typedef {Object} FormState
 * @property {boolean} loading - État de chargement
 * @property {string|null} error - Message d'erreur
 * @property {ValidationError[]} validationErrors - Erreurs de validation
 * @property {boolean} success - Statut de succès
 */

/**
 * Validation des types
 */
export const validateUser = (user) => {
    const required = ['_id', 'email', 'role'];
    return required.every(field => user && user[field]);
};

export const validateEmployee = (employee) => {
    const required = ['nom', 'prenom', 'email'];
    return required.every(field => employee && employee[field]);
};

export const validateDepartment = (department) => {
    const required = ['nom'];
    return required.every(field => department && department[field]);
};

/**
 * Constantes de validation
 */
export const VALIDATION_RULES = {
    email: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: 'Format d\'email invalide'
    },
    phone: {
        pattern: /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/,
        message: 'Format de téléphone invalide'
    },
    password: {
        minLength: 6,
        message: 'Le mot de passe doit contenir au moins 6 caractères'
    },
    required: {
        message: 'Ce champ est requis'
    }
};

/**
 * Utilitaires de type
 */
export const isValidEmail = (email) => {
    return VALIDATION_RULES.email.pattern.test(email);
};

export const isValidPhone = (phone) => {
    return !phone || VALIDATION_RULES.phone.pattern.test(phone);
};

export const isValidPassword = (password) => {
    return password && password.length >= VALIDATION_RULES.password.minLength;
};

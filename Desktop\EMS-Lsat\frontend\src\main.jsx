import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import AuthContext from './context/authContext.jsx'; // Import par défaut
import { NotificationProvider } from './contexts/NotificationContext.jsx';

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <AuthContext>
      <NotificationProvider>
        <App />
      </NotificationProvider>
    </AuthContext>
  </StrictMode>
)

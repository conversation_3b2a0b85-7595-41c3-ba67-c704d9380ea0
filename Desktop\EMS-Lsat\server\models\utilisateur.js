import mongoose from "mongoose";

const utilisateurSchema = new mongoose.Schema({
  email: { type: String, index: true, required: true, unique: true },
  mot_de_passe: { type: String, required: true },
  role: { type: String, enum: ["admin", "rh", "employe"], default: "employe" },
  permissions: [String],
  actif: { type: Boolean, default: true },
  tentatives_connexion: { type: Number, default: 0 },
  derniere_connexion: Date
}, { timestamps: true });

const Utilisateur = mongoose.model("Utilisateur", utilisateurSchema);
export default Utilisateur;
// Utilisateur.createIndexes({ email: 1 }, { unique: true });
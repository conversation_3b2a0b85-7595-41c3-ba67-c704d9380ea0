/**
 * Hook personnalisé pour la gestion des tâches
 */
import { useState, useEffect, useCallback } from 'react';
import { tacheService } from '../services/tacheService';

export const useTaches = () => {
    const [taches, setTaches] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        current: 1,
        pages: 1,
        total: 0
    });

    const fetchTaches = useCallback(async (params = {}) => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await tacheService.getAllTaches(params);
            if (response.success) {
                setTaches(response.data?.taches || []);
                setPagination(response.data?.pagination || { current: 1, pages: 1, total: 0 });
            } else {
                setError(response.message || 'Erreur lors du chargement des tâches');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des tâches');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchTaches();
    }, [fetchTaches]);

    const createTache = async (tacheData) => {
        try {
            const response = await tacheService.createTache(tacheData);
            if (response.success) {
                await fetchTaches(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la création');
            }
        } catch (error) {
            throw error;
        }
    };

    const updateTache = async (id, tacheData) => {
        try {
            const response = await tacheService.updateTache(id, tacheData);
            if (response.success) {
                await fetchTaches(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la mise à jour');
            }
        } catch (error) {
            throw error;
        }
    };

    const deleteTache = async (id) => {
        try {
            const response = await tacheService.deleteTache(id);
            if (response.success) {
                await fetchTaches(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la suppression');
            }
        } catch (error) {
            throw error;
        }
    };

    const ajouterCommentaire = async (id, contenu) => {
        try {
            const response = await tacheService.ajouterCommentaire(id, contenu);
            if (response.success) {
                await fetchTaches(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de l\'ajout du commentaire');
            }
        } catch (error) {
            throw error;
        }
    };

    return {
        taches,
        loading,
        error,
        pagination,
        fetchTaches,
        createTache,
        updateTache,
        deleteTache,
        ajouterCommentaire
    };
};

/**
 * Hook pour obtenir une tâche spécifique
 */
export const useTache = (id) => {
    const [tache, setTache] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchTache = useCallback(async () => {
        if (!id) return;
        
        setLoading(true);
        setError(null);
        
        try {
            const response = await tacheService.getTacheById(id);
            if (response.success) {
                setTache(response.data?.tache);
            } else {
                setError(response.message || 'Tâche non trouvée');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement de la tâche');
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchTache();
    }, [fetchTache]);

    return { tache, loading, error, refetch: fetchTache };
};

/**
 * Hook pour les tâches d'un projet spécifique
 */
export const useTachesByProjet = (projetId) => {
    const [taches, setTaches] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [projet, setProjet] = useState(null);

    const fetchTachesByProjet = useCallback(async () => {
        if (!projetId) return;
        
        setLoading(true);
        setError(null);
        
        try {
            const response = await tacheService.getTachesByProjet(projetId);
            if (response.success) {
                setTaches(response.data?.taches || []);
                setProjet(response.data?.projet);
            } else {
                setError(response.message || 'Erreur lors du chargement des tâches du projet');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des tâches du projet');
        } finally {
            setLoading(false);
        }
    }, [projetId]);

    useEffect(() => {
        fetchTachesByProjet();
    }, [fetchTachesByProjet]);

    const createTache = async (tacheData) => {
        try {
            const response = await tacheService.createTache({
                ...tacheData,
                projet: projetId
            });
            if (response.success) {
                await fetchTachesByProjet(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la création');
            }
        } catch (error) {
            throw error;
        }
    };

    const updateTache = async (id, tacheData) => {
        try {
            const response = await tacheService.updateTache(id, tacheData);
            if (response.success) {
                await fetchTachesByProjet(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la mise à jour');
            }
        } catch (error) {
            throw error;
        }
    };

    const deleteTache = async (id) => {
        try {
            const response = await tacheService.deleteTache(id);
            if (response.success) {
                await fetchTachesByProjet(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la suppression');
            }
        } catch (error) {
            throw error;
        }
    };

    return {
        taches,
        projet,
        loading,
        error,
        fetchTachesByProjet,
        createTache,
        updateTache,
        deleteTache,
        refetch: fetchTachesByProjet
    };
};

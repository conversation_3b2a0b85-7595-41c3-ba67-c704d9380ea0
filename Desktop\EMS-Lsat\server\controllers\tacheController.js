/**
 * Contrôleur pour la gestion des tâches
 */
import Tache from '../models/Tache.js';
import Projet from '../models/Projet.js';
import Employe from '../models/Employe.js';
import { successResponse, errorResponse, notFoundResponse, serverErrorResponse } from '../utils/responseHelper.js';

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> toutes les tâches
 */
export const getAllTaches = async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 10, 
            statut, 
            priorite, 
            projet, 
            employe_assigne,
            search 
        } = req.query;
        
        // Construire le filtre
        const filter = {};
        if (statut) filter.statut = statut;
        if (priorite) filter.priorite = priorite;
        if (projet) filter.projet = projet;
        if (employe_assigne) filter.employe_assigne = employe_assigne;
        
        // Recherche textuelle
        if (search) {
            filter.$or = [
                { nom_tache: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }

        const taches = await Tache.find(filter)
            .populate('projet', 'nom_projet statut')
            .populate('employe_assigne', 'nom prenom email')
            .sort({ date_debut: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Tache.countDocuments(filter);

        // Ajouter les statistiques pour chaque tâche
        const tachesAvecStats = taches.map(tache => {
            const tacheObj = tache.toObject();
            tacheObj.estEnRetard = tache.estEnRetard();
            tacheObj.joursRestants = tache.joursRestants();
            tacheObj.progressionTemps = tache.calculerProgressionTemps();
            return tacheObj;
        });

        return successResponse(res, {
            taches: tachesAvecStats,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total
            }
        }, "Tâches récupérées avec succès");

    } catch (error) {
        console.error('Erreur getAllTaches:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des tâches", error);
    }
};

/**
 * Récupérer une tâche par ID
 */
export const getTacheById = async (req, res) => {
    try {
        const { id } = req.params;

        const tache = await Tache.findById(id)
            .populate('projet', 'nom_projet statut chef_projet')
            .populate('employe_assigne', 'nom prenom email poste')
            .populate('dependances', 'nom_tache statut')
            .populate('commentaires.auteur', 'nom prenom');

        if (!tache) {
            return notFoundResponse(res, "Tâche non trouvée");
        }

        const tacheObj = tache.toObject();
        tacheObj.estEnRetard = tache.estEnRetard();
        tacheObj.joursRestants = tache.joursRestants();
        tacheObj.progressionTemps = tache.calculerProgressionTemps();

        return successResponse(res, { tache: tacheObj }, "Tâche récupérée avec succès");

    } catch (error) {
        console.error('Erreur getTacheById:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération de la tâche", error);
    }
};

/**
 * Créer une nouvelle tâche
 */
export const createTache = async (req, res) => {
    try {
        const { 
            nom_tache, 
            description, 
            date_debut, 
            date_fin_prevue, 
            projet, 
            employe_assigne,
            priorite,
            temps_estime,
            dependances,
            tags
        } = req.body;

        // Validation des champs requis
        if (!nom_tache || !description || !date_debut || !date_fin_prevue || !projet || !employe_assigne) {
            return errorResponse(res, "Nom, description, dates, projet et employé assigné sont requis", 400);
        }

        // Vérifier que le projet existe
        const projetExists = await Projet.findById(projet);
        if (!projetExists) {
            return notFoundResponse(res, "Projet non trouvé");
        }

        // Vérifier que l'employé existe
        const employeExists = await Employe.findById(employe_assigne);
        if (!employeExists) {
            return notFoundResponse(res, "Employé non trouvé");
        }

        // Validation des dates
        const dateDebut = new Date(date_debut);
        const dateFinPrevue = new Date(date_fin_prevue);

        if (dateFinPrevue <= dateDebut) {
            return errorResponse(res, "La date de fin prévue doit être postérieure à la date de début", 400);
        }

        // Vérifier que les dates sont dans la plage du projet
        if (dateDebut < projetExists.date_debut || dateFinPrevue > projetExists.date_fin_prevue) {
            return errorResponse(res, "Les dates de la tâche doivent être dans la plage du projet", 400);
        }

        // Créer la nouvelle tâche
        const newTache = new Tache({
            nom_tache: nom_tache.trim(),
            description: description.trim(),
            date_debut: dateDebut,
            date_fin_prevue: dateFinPrevue,
            projet,
            employe_assigne,
            priorite: priorite || 'Moyenne',
            temps_estime: parseFloat(temps_estime) || 0,
            dependances: dependances || [],
            tags: tags || []
        });

        await newTache.save();

        // Ajouter la tâche au projet
        projetExists.taches.push(newTache._id);
        await projetExists.save();

        // Récupérer la tâche avec les données populées
        const populatedTache = await Tache.findById(newTache._id)
            .populate('projet', 'nom_projet')
            .populate('employe_assigne', 'nom prenom email');

        return successResponse(res, { tache: populatedTache }, "Tâche créée avec succès", 201);

    } catch (error) {
        console.error('Erreur createTache:', error);
        return serverErrorResponse(res, "Erreur lors de la création de la tâche", error);
    }
};

/**
 * Mettre à jour une tâche
 */
export const updateTache = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        const tache = await Tache.findById(id);
        if (!tache) {
            return notFoundResponse(res, "Tâche non trouvée");
        }

        // Vérifier l'employé si fourni
        if (updateData.employe_assigne && updateData.employe_assigne !== tache.employe_assigne.toString()) {
            const employeExists = await Employe.findById(updateData.employe_assigne);
            if (!employeExists) {
                return notFoundResponse(res, "Employé non trouvé");
            }
        }

        // Validation des dates si fournies
        if (updateData.date_debut || updateData.date_fin_prevue) {
            const dateDebut = updateData.date_debut ? new Date(updateData.date_debut) : tache.date_debut;
            const dateFinPrevue = updateData.date_fin_prevue ? new Date(updateData.date_fin_prevue) : tache.date_fin_prevue;

            if (dateFinPrevue <= dateDebut) {
                return errorResponse(res, "La date de fin prévue doit être postérieure à la date de début", 400);
            }
        }

        // Mettre à jour les champs
        Object.keys(updateData).forEach(key => {
            if (updateData[key] !== undefined && key !== '_id') {
                tache[key] = updateData[key];
            }
        });

        await tache.save();

        // Récupérer la tâche mise à jour avec les données populées
        const updatedTache = await Tache.findById(id)
            .populate('projet', 'nom_projet')
            .populate('employe_assigne', 'nom prenom email');

        return successResponse(res, { tache: updatedTache }, "Tâche mise à jour avec succès");

    } catch (error) {
        console.error('Erreur updateTache:', error);
        return serverErrorResponse(res, "Erreur lors de la mise à jour de la tâche", error);
    }
};

/**
 * Supprimer une tâche
 */
export const deleteTache = async (req, res) => {
    try {
        const { id } = req.params;

        const tache = await Tache.findById(id);
        if (!tache) {
            return notFoundResponse(res, "Tâche non trouvée");
        }

        // Vérifier si la tâche peut être supprimée
        if (tache.statut === 'En cours') {
            return errorResponse(res, 
                "Impossible de supprimer une tâche en cours. Veuillez d'abord la terminer ou l'annuler.", 
                400
            );
        }

        // Retirer la tâche du projet
        await Projet.findByIdAndUpdate(
            tache.projet,
            { $pull: { taches: id } }
        );

        // Supprimer les références dans les dépendances d'autres tâches
        await Tache.updateMany(
            { dependances: id },
            { $pull: { dependances: id } }
        );

        // Supprimer la tâche
        await Tache.findByIdAndDelete(id);

        return successResponse(res, null, "Tâche supprimée avec succès");

    } catch (error) {
        console.error('Erreur deleteTache:', error);
        return serverErrorResponse(res, "Erreur lors de la suppression de la tâche", error);
    }
};

/**
 * Ajouter un commentaire à une tâche
 */
export const ajouterCommentaire = async (req, res) => {
    try {
        const { id } = req.params;
        const { contenu } = req.body;

        if (!contenu || contenu.trim().length === 0) {
            return errorResponse(res, "Le contenu du commentaire est requis", 400);
        }

        const tache = await Tache.findById(id);
        if (!tache) {
            return notFoundResponse(res, "Tâche non trouvée");
        }

        tache.commentaires.push({
            auteur: req.user.id,
            contenu: contenu.trim(),
            date_creation: new Date()
        });

        await tache.save();

        // Récupérer la tâche avec les commentaires populés
        const tacheAvecCommentaires = await Tache.findById(id)
            .populate('commentaires.auteur', 'nom prenom');

        return successResponse(res, { 
            tache: tacheAvecCommentaires,
            nouveauCommentaire: tache.commentaires[tache.commentaires.length - 1]
        }, "Commentaire ajouté avec succès");

    } catch (error) {
        console.error('Erreur ajouterCommentaire:', error);
        return serverErrorResponse(res, "Erreur lors de l'ajout du commentaire", error);
    }
};

/**
 * Obtenir les tâches d'un projet spécifique
 */
export const getTachesByProjet = async (req, res) => {
    try {
        const { projetId } = req.params;

        // Vérifier que le projet existe
        const projet = await Projet.findById(projetId);
        if (!projet) {
            return notFoundResponse(res, "Projet non trouvé");
        }

        const taches = await Tache.find({ projet: projetId })
            .populate('employe_assigne', 'nom prenom email')
            .sort({ date_debut: 1 });

        // Ajouter les statistiques pour chaque tâche
        const tachesAvecStats = taches.map(tache => {
            const tacheObj = tache.toObject();
            tacheObj.estEnRetard = tache.estEnRetard();
            tacheObj.joursRestants = tache.joursRestants();
            tacheObj.progressionTemps = tache.calculerProgressionTemps();
            return tacheObj;
        });

        return successResponse(res, { 
            projet: {
                _id: projet._id,
                nom_projet: projet.nom_projet,
                statut: projet.statut
            },
            taches: tachesAvecStats 
        }, "Tâches du projet récupérées avec succès");

    } catch (error) {
        console.error('Erreur getTachesByProjet:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des tâches", error);
    }
};

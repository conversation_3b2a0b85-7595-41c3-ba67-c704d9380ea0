import { Outlet } from "react-router-dom";
import AdminSidebar from "../components/dashboard/AdminSidebar.jsx";
import ModernNavbar from "../components/dashboard/ModernNavbar.jsx";
import Footer from "./Footer.jsx";
import { useAuth } from "../context/authContext.jsx";

const AdminPage = () => {
  const { utilisateur, loading } = useAuth();

  // Vérification de sécurité - s'assurer que l'utilisateur est bien admin
  if (!loading && utilisateur?.role !== 'admin') {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
      }}>
        <div style={{
          padding: '3rem',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderRadius: '20px',
          boxShadow: '0 25px 50px rgba(0, 0, 0, 0.15)',
          backdropFilter: 'blur(10px)',
          textAlign: 'center',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          maxWidth: '400px'
        }}>
          <div style={{
            width: '80px',
            height: '80px',
            backgroundColor: '#fee2e2',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 1.5rem auto'
          }}>
            <span style={{ fontSize: '2rem' }}>🚫</span>
          </div>
          <h2 style={{ margin: '0 0 1rem 0', color: '#374151' }}>Accès Restreint</h2>
          <p style={{ margin: '0 0 1.5rem 0', color: '#6b7280', lineHeight: '1.6' }}>
            Cette section est réservée aux administrateurs. Veuillez vous connecter avec les privilèges appropriés.
          </p>
          <button
            onClick={() => window.location.href = '/'}
            style={{
              padding: '0.75rem 2rem',
              backgroundColor: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              cursor: 'pointer',
              fontWeight: '500',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#5a67d8'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#667eea'}
          >
            Retour à l'accueil
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
      }}>
        <div style={{
          padding: '3rem',
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderRadius: '20px',
          boxShadow: '0 25px 50px rgba(0, 0, 0, 0.15)',
          backdropFilter: 'blur(10px)',
          textAlign: 'center',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}>
          <div style={{
            width: '60px',
            height: '60px',
            border: '4px solid #f3f3f3',
            borderTop: '4px solid #667eea',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1.5rem auto'
          }}></div>
          <h3 style={{ margin: '0 0 0.5rem 0', color: '#374151' }}>Chargement...</h3>
          <p style={{ margin: 0, color: '#6b7280' }}>Initialisation de l'interface administrateur</p>
        </div>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div style={{
      display: "flex",
      flexDirection: "column",
      minHeight: "100vh",
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
    }}>
      <div style={{ display: "flex", flex: 1 }}>
        {/* Admin Sidebar */}
        <AdminSidebar />

        {/* Main content area */}
        <div style={{
          flex: 1,
          marginLeft: "280px",
          transition: "margin-left 0.3s ease",
          display: "flex",
          flexDirection: "column"
        }}>
          {/* Navbar */}
          <ModernNavbar />

          {/* Page content */}
          <div style={{
            flex: 1,
            padding: "2rem",
            marginTop: "4rem",
            background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
            minHeight: "calc(100vh - 4rem)"
          }}>
            <Outlet />
          </div>

          {/* Footer */}
          <Footer />
        </div>
      </div>
    </div>
  );
};

export default AdminPage;

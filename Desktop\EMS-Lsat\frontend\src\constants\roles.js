/**
 * Constantes pour les rôles utilisateur
 */

export const ROLES = {
    ADMIN: 'admin',
    RH: 'RH',
    EMPLOYEE: 'employee'
};

export const ROLE_LABELS = {
    [ROLES.ADMIN]: 'Administrateur',
    [ROLES.RH]: 'Ressources Humaines',
    [ROLES.EMPLOYEE]: 'Employé'
};

export const ROLE_COLORS = {
    [ROLES.ADMIN]: '#dc3545', // Rouge
    [ROLES.RH]: '#007bff',    // Bleu
    [ROLES.EMPLOYEE]: '#28a745' // Vert
};

/**
 * Vérifier si un rôle est administratif (admin ou RH)
 * @param {string} role - Rôle à vérifier
 * @returns {boolean} True si le rôle est administratif
 */
export const isAdminRole = (role) => {
    return role === ROLES.ADMIN || role === ROLES.RH;
};

/**
 * Obtenir le label d'un rôle
 * @param {string} role - Rôle
 * @returns {string} Label du rôle
 */
export const getRoleLabel = (role) => {
    return ROLE_LABELS[role] || role;
};

/**
 * Obtenir la couleur d'un rôle
 * @param {string} role - Rôle
 * @returns {string} Couleur du rôle
 */
export const getRoleColor = (role) => {
    return ROLE_COLORS[role] || '#6c757d';
};

/**
 * Contrôleur pour la gestion des contrats
 */
import Contrat from '../models/Contrat.js';
import Employe from '../models/Employe.js';
import { successResponse, errorResponse, notFoundResponse, serverErrorResponse } from '../utils/responseHelper.js';

/**
 * Récupérer tous les contrats
 */
export const getAllContrats = async (req, res) => {
    try {
        const { page = 1, limit = 10, type_contrat, statut, employe } = req.query;
        
        // Construire le filtre
        const filter = {};
        if (type_contrat) filter.type_contrat = type_contrat;
        if (statut) filter.statut = statut;
        if (employe) filter.employe = employe;

        const contrats = await Contrat.find(filter)
            .populate('employe', 'nom prenom email poste departement service')
            .sort({ date_debut: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Contrat.countDocuments(filter);

        return successResponse(res, {
            contrats,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total
            }
        }, "Contrats récupérés avec succès");

    } catch (error) {
        console.error('Erreur getAllContrats:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des contrats", error);
    }
};

/**
 * Récupérer un contrat par ID
 */
export const getContratById = async (req, res) => {
    try {
        const { id } = req.params;

        const contrat = await Contrat.findById(id)
            .populate('employe', 'nom prenom email poste departement service');

        if (!contrat) {
            return notFoundResponse(res, "Contrat non trouvé");
        }

        return successResponse(res, { contrat }, "Contrat récupéré avec succès");

    } catch (error) {
        console.error('Erreur getContratById:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération du contrat", error);
    }
};

/**
 * Créer un nouveau contrat
 */
export const createContrat = async (req, res) => {
    try {
        const { 
            type_contrat, 
            date_debut, 
            date_fin, 
            salaire, 
            employe, 
            description,
            conditions_particulieres 
        } = req.body;

        // Validation des champs requis
        if (!type_contrat || !date_debut || !salaire || !employe) {
            return errorResponse(res, "Type de contrat, date de début, salaire et employé sont requis", 400);
        }

        // Vérifier que l'employé existe
        const employeExists = await Employe.findById(employe);
        if (!employeExists) {
            return notFoundResponse(res, "Employé non trouvé");
        }

        // Validation des dates
        const dateDebut = new Date(date_debut);
        const dateFin = date_fin ? new Date(date_fin) : null;

        if (dateFin && dateFin <= dateDebut) {
            return errorResponse(res, "La date de fin doit être postérieure à la date de début", 400);
        }

        // Validation spécifique selon le type de contrat
        if (type_contrat !== 'CDI' && !dateFin) {
            return errorResponse(res, "Une date de fin est requise pour ce type de contrat", 400);
        }

        // Vérifier s'il y a déjà un contrat actif pour cet employé
        const contratActif = await Contrat.findOne({ 
            employe, 
            statut: 'Actif',
            date_debut: { $lte: new Date() },
            $or: [
                { date_fin: { $exists: false } },
                { date_fin: null },
                { date_fin: { $gte: new Date() } }
            ]
        });

        if (contratActif) {
            return errorResponse(res, "Cet employé a déjà un contrat actif", 400);
        }

        // Créer le nouveau contrat
        const newContrat = new Contrat({
            type_contrat,
            date_debut: dateDebut,
            date_fin: dateFin,
            salaire: parseFloat(salaire),
            employe,
            description: description?.trim(),
            conditions_particulieres: conditions_particulieres?.trim()
        });

        await newContrat.save();

        // Récupérer le contrat avec les données populées
        const populatedContrat = await Contrat.findById(newContrat._id)
            .populate('employe', 'nom prenom email poste');

        return successResponse(res, { contrat: populatedContrat }, "Contrat créé avec succès", 201);

    } catch (error) {
        console.error('Erreur createContrat:', error);
        return serverErrorResponse(res, "Erreur lors de la création du contrat", error);
    }
};

/**
 * Mettre à jour un contrat
 */
export const updateContrat = async (req, res) => {
    try {
        const { id } = req.params;
        const { 
            type_contrat, 
            date_debut, 
            date_fin, 
            salaire, 
            employe, 
            statut,
            description,
            conditions_particulieres 
        } = req.body;

        const contrat = await Contrat.findById(id);
        if (!contrat) {
            return notFoundResponse(res, "Contrat non trouvé");
        }

        // Vérifier que l'employé existe si fourni
        if (employe && employe !== contrat.employe.toString()) {
            const employeExists = await Employe.findById(employe);
            if (!employeExists) {
                return notFoundResponse(res, "Employé non trouvé");
            }
        }

        // Validation des dates si fournies
        if (date_debut || date_fin) {
            const dateDebut = date_debut ? new Date(date_debut) : contrat.date_debut;
            const dateFin = date_fin ? new Date(date_fin) : contrat.date_fin;

            if (dateFin && dateFin <= dateDebut) {
                return errorResponse(res, "La date de fin doit être postérieure à la date de début", 400);
            }
        }

        // Mettre à jour les champs
        if (type_contrat) contrat.type_contrat = type_contrat;
        if (date_debut) contrat.date_debut = new Date(date_debut);
        if (date_fin !== undefined) contrat.date_fin = date_fin ? new Date(date_fin) : null;
        if (salaire) contrat.salaire = parseFloat(salaire);
        if (employe) contrat.employe = employe;
        if (statut) contrat.statut = statut;
        if (description !== undefined) contrat.description = description?.trim();
        if (conditions_particulieres !== undefined) contrat.conditions_particulieres = conditions_particulieres?.trim();

        await contrat.save();

        // Récupérer le contrat mis à jour avec les données populées
        const updatedContrat = await Contrat.findById(id)
            .populate('employe', 'nom prenom email poste');

        return successResponse(res, { contrat: updatedContrat }, "Contrat mis à jour avec succès");

    } catch (error) {
        console.error('Erreur updateContrat:', error);
        return serverErrorResponse(res, "Erreur lors de la mise à jour du contrat", error);
    }
};

/**
 * Supprimer un contrat
 */
export const deleteContrat = async (req, res) => {
    try {
        const { id } = req.params;

        const contrat = await Contrat.findById(id);
        if (!contrat) {
            return notFoundResponse(res, "Contrat non trouvé");
        }

        // Vérifier si le contrat est actif
        if (contrat.statut === 'Actif' && contrat.isActif()) {
            return errorResponse(res, 
                "Impossible de supprimer un contrat actif. Veuillez d'abord le suspendre ou le terminer.", 
                400
            );
        }

        await Contrat.findByIdAndDelete(id);

        return successResponse(res, null, "Contrat supprimé avec succès");

    } catch (error) {
        console.error('Erreur deleteContrat:', error);
        return serverErrorResponse(res, "Erreur lors de la suppression du contrat", error);
    }
};

/**
 * Obtenir les statistiques des contrats
 */
export const getContratStats = async (req, res) => {
    try {
        const totalContrats = await Contrat.countDocuments();
        const contratsActifs = await Contrat.countDocuments({ statut: 'Actif' });

        // Contrats par type
        const contratsByType = await Contrat.aggregate([
            {
                $group: {
                    _id: '$type_contrat',
                    count: { $sum: 1 },
                    salaireMoyen: { $avg: '$salaire' }
                }
            },
            {
                $sort: { count: -1 }
            }
        ]);

        // Contrats par statut
        const contratsByStatut = await Contrat.aggregate([
            {
                $group: {
                    _id: '$statut',
                    count: { $sum: 1 }
                }
            }
        ]);

        // Contrats expirant bientôt (dans les 30 jours)
        const dateLimite = new Date();
        dateLimite.setDate(dateLimite.getDate() + 30);

        const contratsExpirants = await Contrat.find({
            statut: 'Actif',
            date_fin: { 
                $exists: true, 
                $ne: null, 
                $lte: dateLimite,
                $gte: new Date()
            }
        })
        .populate('employe', 'nom prenom email')
        .sort({ date_fin: 1 });

        return successResponse(res, {
            total: totalContrats,
            actifs: contratsActifs,
            termines: totalContrats - contratsActifs,
            byType: contratsByType,
            byStatut: contratsByStatut,
            expirantsBientot: contratsExpirants
        }, "Statistiques des contrats récupérées avec succès");

    } catch (error) {
        console.error('Erreur getContratStats:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des statistiques", error);
    }
};

/**
 * Obtenir les contrats d'un employé spécifique
 */
export const getContratsByEmploye = async (req, res) => {
    try {
        const { employeId } = req.params;

        // Vérifier que l'employé existe
        const employe = await Employe.findById(employeId);
        if (!employe) {
            return notFoundResponse(res, "Employé non trouvé");
        }

        const contrats = await Contrat.find({ employe: employeId })
            .sort({ date_debut: -1 });

        return successResponse(res, { 
            employe: {
                _id: employe._id,
                nom: employe.nom,
                prenom: employe.prenom,
                email: employe.email
            },
            contrats 
        }, "Contrats de l'employé récupérés avec succès");

    } catch (error) {
        console.error('Erreur getContratsByEmploye:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des contrats", error);
    }
};

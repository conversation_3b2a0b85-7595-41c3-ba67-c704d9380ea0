/**
 * Variables CSS globales pour l'application EMS
 */

:root {
  /* Couleurs principales */
  --primary-color: #007bff;
  --primary-dark: #0056b3;
  --primary-light: #66b3ff;
  
  --secondary-color: #6c757d;
  --secondary-dark: #545b62;
  --secondary-light: #adb5bd;
  
  /* Couleurs de statut */
  --success-color: #28a745;
  --success-dark: #1e7e34;
  --success-light: #d4edda;
  
  --danger-color: #dc3545;
  --danger-dark: #c82333;
  --danger-light: #f8d7da;
  
  --warning-color: #ffc107;
  --warning-dark: #e0a800;
  --warning-light: #fff3cd;
  
  --info-color: #17a2b8;
  --info-dark: #138496;
  --info-light: #d1ecf1;
  
  /* Couleurs de rôles */
  --admin-color: #dc3545;
  --rh-color: #007bff;
  --employee-color: #28a745;
  
  /* Couleurs de fond */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-dark: #343a40;
  
  /* Couleurs de texte */
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;
  --text-white: #ffffff;
  
  /* Couleurs de bordure */
  --border-color: #dee2e6;
  --border-light: #f1f3f4;
  --border-dark: #adb5bd;
  
  /* Ombres */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  /* Rayons de bordure */
  --border-radius-sm: 0.25rem;
  --border-radius: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;
  
  /* Espacements */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
  
  /* Tailles de police */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* Poids de police */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Hauteurs de ligne */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  
  /* Largeurs de conteneur */
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;
  
  /* Breakpoints */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
}

/**
 * Hook personnalisé pour la gestion des services
 */
import { useState, useEffect, useCallback } from 'react';
import { serviceService } from '../services/serviceService';

export const useServices = () => {
    const [services, setServices] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        current: 1,
        pages: 1,
        total: 0
    });

    const fetchServices = useCallback(async (params = {}) => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await serviceService.getAllServices(params);
            if (response.success) {
                setServices(response.data?.services || []);
                setPagination(response.data?.pagination || { current: 1, pages: 1, total: 0 });
            } else {
                setError(response.message || 'Erreur lors du chargement des services');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des services');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchServices();
    }, [fetchServices]);

    const createService = async (serviceData) => {
        try {
            const response = await serviceService.createService(serviceData);
            if (response.success) {
                await fetchServices(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la création');
            }
        } catch (error) {
            throw error;
        }
    };

    const updateService = async (id, serviceData) => {
        try {
            const response = await serviceService.updateService(id, serviceData);
            if (response.success) {
                await fetchServices(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la mise à jour');
            }
        } catch (error) {
            throw error;
        }
    };

    const deleteService = async (id) => {
        try {
            const response = await serviceService.deleteService(id);
            if (response.success) {
                await fetchServices(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la suppression');
            }
        } catch (error) {
            throw error;
        }
    };

    return {
        services,
        loading,
        error,
        pagination,
        fetchServices,
        createService,
        updateService,
        deleteService
    };
};

/**
 * Hook pour obtenir un service spécifique
 */
export const useService = (id) => {
    const [service, setService] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchService = useCallback(async () => {
        if (!id) return;
        
        setLoading(true);
        setError(null);
        
        try {
            const response = await serviceService.getServiceById(id);
            if (response.success) {
                setService(response.data?.service);
            } else {
                setError(response.message || 'Service non trouvé');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement du service');
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchService();
    }, [fetchService]);

    return { service, loading, error, refetch: fetchService };
};

/**
 * Hook pour les statistiques des services
 */
export const useServiceStats = () => {
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchStats = useCallback(async () => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await serviceService.getServiceStats();
            if (response.success) {
                setStats(response.data);
            } else {
                setError(response.message || 'Erreur lors du chargement des statistiques');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des statistiques');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchStats();
    }, [fetchStats]);

    return { stats, loading, error, refetch: fetchStats };
};

/**
 * Hook pour obtenir les services d'un département
 */
export const useServicesByDepartment = (departmentId) => {
    const [services, setServices] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchServicesByDepartment = useCallback(async () => {
        if (!departmentId) return;
        
        setLoading(true);
        setError(null);
        
        try {
            const response = await serviceService.getServicesByDepartment(departmentId);
            if (response.success) {
                setServices(response.data?.services || []);
            } else {
                setError(response.message || 'Erreur lors du chargement des services');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des services');
        } finally {
            setLoading(false);
        }
    }, [departmentId]);

    useEffect(() => {
        fetchServicesByDepartment();
    }, [fetchServicesByDepartment]);

    return { services, loading, error, refetch: fetchServicesByDepartment };
};

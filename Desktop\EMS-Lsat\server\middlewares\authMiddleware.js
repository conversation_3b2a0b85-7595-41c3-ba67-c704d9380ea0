import jwt from "jsonwebtoken";
import Utilisateur from "../models/utilisateur.js";

const verifierutlisateur = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split(' ')[1];
        if (!token) {
            return res.status(401).json({ success: false, error: "Token manquant" });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        if (!decoded) {
            return res.status(401).json({ success: false, error: "Token invalide" });
        }

        const utilisateur = await Utilisateur.findById(decoded._id).select("-mot_de_passe");
        if (!utilisateur) {
            return res.status(404).json({ success: false, error: "Utilisateur non trouvé" });
        }

        req.utilisateur = utilisateur; // Ajoutez l'utilisateur au req
        next(); // Passez au middleware suivant
    } catch (error) {
        console.error("Erreur dans le middleware authMiddleware :", error);
        return res.status(500).json({ success: false, error: "Erreur interne du serveur" });
    }
};

export default verifierutlisateur;
/**
 * Contrôleur pour la gestion des congés
 */
import Conge from '../models/Conge.js';
import TypeConge from '../models/TypeConge.js';
import Employe from '../models/Employe.js';
import { successResponse, errorResponse, notFoundResponse, serverErrorResponse } from '../utils/responseHelper.js';

/**
 * Créer une nouvelle demande de congé
 */
export const creerDemandeConge = async (req, res) => {
    try {
        const {
            type_conge,
            date_debut,
            date_fin,
            commentaire
        } = req.body;

        // Validation des champs requis
        if (!type_conge || !date_debut || !date_fin) {
            return errorResponse(res, "Type de congé, date de début et date de fin sont requis", 400);
        }

        // Récupérer l'employé connecté
        const employe = await Employe.findOne({ utilisateur: req.utilisateur._id });
        if (!employe) {
            return notFoundResponse(res, "Employé non trouvé");
        }

        // Vérifier que le type de congé existe
        const typeCongeExists = await TypeConge.findById(type_conge);
        if (!typeCongeExists) {
            return notFoundResponse(res, "Type de congé non trouvé");
        }

        // Validation des dates
        const dateDebut = new Date(date_debut);
        const dateFin = new Date(date_fin);
        const today = new Date();

        if (dateDebut < today) {
            return errorResponse(res, "La date de début ne peut pas être dans le passé", 400);
        }

        if (dateFin <= dateDebut) {
            return errorResponse(res, "La date de fin doit être postérieure à la date de début", 400);
        }

        // Calculer le nombre de jours
        const diffTime = Math.abs(dateFin - dateDebut);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // Vérifier les chevauchements avec d'autres congés
        const chevauchement = await Conge.findOne({
            employe: employe._id,
            statut: { $in: ['en attente', 'validé'] },
            $or: [
                {
                    date_debut: { $lte: dateFin },
                    date_fin: { $gte: dateDebut }
                }
            ]
        });

        if (chevauchement) {
            return errorResponse(res, "Cette période chevauche avec une autre demande de congé", 400);
        }

        // Créer la demande de congé
        const nouvelleDemandeConge = new Conge({
            employe: employe._id,
            type_conge,
            date_debut: dateDebut,
            date_fin: dateFin,
            jours_non_ouvrables: diffDays,
            commentaire: commentaire?.trim(),
            statut: 'en attente',
            solde_avant: employe.solde_conges || 0,
            solde_apres: (employe.solde_conges || 0) - diffDays
        });

        await nouvelleDemandeConge.save();

        // Populer les données pour la réponse
        await nouvelleDemandeConge.populate([
            { path: 'employe', select: 'nom prenom email' },
            { path: 'type_conge', select: 'nom_type duree_max' }
        ]);

        return successResponse(res, nouvelleDemandeConge, "Demande de congé créée avec succès", 201);

    } catch (error) {
        console.error('Erreur lors de la création de la demande de congé:', error);
        return serverErrorResponse(res, "Erreur lors de la création de la demande de congé");
    }
};

/**
 * Récupérer les demandes de congé de l'employé connecté
 */
export const getMesDemandesConge = async (req, res) => {
    try {
        const { page = 1, limit = 10, statut } = req.query;

        // Récupérer l'employé connecté
        const employe = await Employe.findOne({ utilisateur: req.utilisateur._id });
        if (!employe) {
            return notFoundResponse(res, "Employé non trouvé");
        }

        // Construire le filtre
        const filter = { employe: employe._id };
        if (statut) filter.statut = statut;

        const demandes = await Conge.find(filter)
            .populate('type_conge', 'nom_type duree_max')
            .sort({ createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Conge.countDocuments(filter);

        return successResponse(res, {
            demandes,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total
            }
        }, "Demandes de congé récupérées avec succès");

    } catch (error) {
        console.error('Erreur lors de la récupération des demandes:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des demandes de congé");
    }
};

/**
 * Récupérer toutes les demandes de congé (Admin/RH)
 */
export const getToutesDemandesConge = async (req, res) => {
    try {
        const { page = 1, limit = 10, statut, employe, type_conge } = req.query;

        // Construire le filtre
        const filter = {};
        if (statut) filter.statut = statut;
        if (employe) filter.employe = employe;
        if (type_conge) filter.type_conge = type_conge;

        const demandes = await Conge.find(filter)
            .populate('employe', 'nom prenom email departement service')
            .populate('type_conge', 'nom_type duree_max')
            .sort({ createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Conge.countDocuments(filter);

        return successResponse(res, {
            demandes,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total
            }
        }, "Toutes les demandes de congé récupérées avec succès");

    } catch (error) {
        console.error('Erreur lors de la récupération de toutes les demandes:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des demandes de congé");
    }
};

/**
 * Approuver une demande de congé
 */
export const approuverDemandeConge = async (req, res) => {
    try {
        const { id } = req.params;
        const { commentaire_admin } = req.body;

        const demande = await Conge.findById(id)
            .populate('employe', 'nom prenom email solde_conges')
            .populate('type_conge', 'nom_type');

        if (!demande) {
            return notFoundResponse(res, "Demande de congé non trouvée");
        }

        if (demande.statut !== 'en attente') {
            return errorResponse(res, "Cette demande a déjà été traitée", 400);
        }

        // Mettre à jour le statut
        demande.statut = 'validé';
        demande.commentaire_admin = commentaire_admin?.trim();
        demande.date_traitement = new Date();
        demande.traite_par = req.utilisateur._id;

        await demande.save();

        // Mettre à jour le solde de congés de l'employé
        const employe = await Employe.findById(demande.employe._id);
        if (employe) {
            employe.solde_conges = (employe.solde_conges || 0) - demande.jours_non_ouvrables;
            await employe.save();
        }

        return successResponse(res, demande, "Demande de congé approuvée avec succès");

    } catch (error) {
        console.error('Erreur lors de l\'approbation:', error);
        return serverErrorResponse(res, "Erreur lors de l'approbation de la demande");
    }
};

/**
 * Refuser une demande de congé
 */
export const refuserDemandeConge = async (req, res) => {
    try {
        const { id } = req.params;
        const { motif_refus } = req.body;

        if (!motif_refus) {
            return errorResponse(res, "Le motif de refus est requis", 400);
        }

        const demande = await Conge.findById(id)
            .populate('employe', 'nom prenom email')
            .populate('type_conge', 'nom_type');

        if (!demande) {
            return notFoundResponse(res, "Demande de congé non trouvée");
        }

        if (demande.statut !== 'en attente') {
            return errorResponse(res, "Cette demande a déjà été traitée", 400);
        }

        // Mettre à jour le statut
        demande.statut = 'refusé';
        demande.motif_refus = motif_refus.trim();
        demande.date_traitement = new Date();
        demande.traite_par = req.utilisateur._id;

        await demande.save();

        return successResponse(res, demande, "Demande de congé refusée");

    } catch (error) {
        console.error('Erreur lors du refus:', error);
        return serverErrorResponse(res, "Erreur lors du refus de la demande");
    }
};

/**
 * Récupérer les types de congé disponibles
 */
export const getTypesConge = async (req, res) => {
    try {
        const types = await TypeConge.find({ actif: true }).sort({ nom_type: 1 });

        return successResponse(res, types, "Types de congé récupérés avec succès");

    } catch (error) {
        console.error('Erreur lors de la récupération des types de congé:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des types de congé");
    }
};

/**
 * Récupérer les statistiques des congés
 */
export const getStatistiquesConge = async (req, res) => {
    try {
        // Statistiques générales
        const totalDemandes = await Conge.countDocuments();
        const demandesEnAttente = await Conge.countDocuments({ statut: 'en attente' });
        const demandesApprouvees = await Conge.countDocuments({ statut: 'validé' });
        const demandesRefusees = await Conge.countDocuments({ statut: 'refusé' });

        // Statistiques par type de congé
        const demandesParType = await Conge.aggregate([
            {
                $lookup: {
                    from: 'typeconges',
                    localField: 'type_conge',
                    foreignField: '_id',
                    as: 'type_info'
                }
            },
            {
                $unwind: '$type_info'
            },
            {
                $group: {
                    _id: '$type_info.nom_type',
                    count: { $sum: 1 },
                    jours_total: { $sum: '$jours_non_ouvrables' }
                }
            },
            {
                $sort: { count: -1 }
            }
        ]);

        // Statistiques par mois (derniers 12 mois)
        const demandesParMois = await Conge.aggregate([
            {
                $match: {
                    createdAt: {
                        $gte: new Date(new Date().setFullYear(new Date().getFullYear() - 1))
                    }
                }
            },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' }
                    },
                    count: { $sum: 1 },
                    jours_total: { $sum: '$jours_non_ouvrables' }
                }
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 }
            }
        ]);

        return successResponse(res, {
            general: {
                total_demandes: totalDemandes,
                demandes_en_attente: demandesEnAttente,
                demandes_approuvees: demandesApprouvees,
                demandes_refusees: demandesRefusees
            },
            par_type: demandesParType,
            par_mois: demandesParMois
        }, "Statistiques des congés récupérées avec succès");

    } catch (error) {
        console.error('Erreur lors du calcul des statistiques:', error);
        return serverErrorResponse(res, "Erreur lors du calcul des statistiques");
    }
};

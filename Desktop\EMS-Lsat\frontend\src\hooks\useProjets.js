/**
 * Hook personnalisé pour la gestion des projets
 */
import { useState, useEffect, useCallback } from 'react';
import { projetService } from '../services/projetService';

export const useProjets = () => {
    const [projets, setProjets] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        current: 1,
        pages: 1,
        total: 0
    });

    const fetchProjets = useCallback(async (params = {}) => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await projetService.getAllProjets(params);
            if (response.success) {
                setProjets(response.data?.projets || []);
                setPagination(response.data?.pagination || { current: 1, pages: 1, total: 0 });
            } else {
                setError(response.message || 'Erreur lors du chargement des projets');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des projets');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchProjets();
    }, [fetchProjets]);

    const createProjet = async (projetData) => {
        try {
            const response = await projetService.createProjet(projetData);
            if (response.success) {
                await fetchProjets(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la création');
            }
        } catch (error) {
            throw error;
        }
    };

    const updateProjet = async (id, projetData) => {
        try {
            const response = await projetService.updateProjet(id, projetData);
            if (response.success) {
                await fetchProjets(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la mise à jour');
            }
        } catch (error) {
            throw error;
        }
    };

    const deleteProjet = async (id) => {
        try {
            const response = await projetService.deleteProjet(id);
            if (response.success) {
                await fetchProjets(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la suppression');
            }
        } catch (error) {
            throw error;
        }
    };

    const archiverProjet = async (id, archiveData) => {
        try {
            const response = await projetService.archiverProjet(id, archiveData);
            if (response.success) {
                await fetchProjets(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de l\'archivage');
            }
        } catch (error) {
            throw error;
        }
    };

    return {
        projets,
        loading,
        error,
        pagination,
        fetchProjets,
        createProjet,
        updateProjet,
        deleteProjet,
        archiverProjet
    };
};

/**
 * Hook pour obtenir un projet spécifique
 */
export const useProjet = (id) => {
    const [projet, setProjet] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchProjet = useCallback(async () => {
        if (!id) return;
        
        setLoading(true);
        setError(null);
        
        try {
            const response = await projetService.getProjetById(id);
            if (response.success) {
                setProjet(response.data?.projet);
            } else {
                setError(response.message || 'Projet non trouvé');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement du projet');
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchProjet();
    }, [fetchProjet]);

    return { projet, loading, error, refetch: fetchProjet };
};

/**
 * Hook pour les statistiques des projets
 */
export const useProjetStats = () => {
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchStats = useCallback(async () => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await projetService.getProjetStats();
            if (response.success) {
                setStats(response.data);
            } else {
                setError(response.message || 'Erreur lors du chargement des statistiques');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des statistiques');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchStats();
    }, [fetchStats]);

    return { stats, loading, error, refetch: fetchStats };
};

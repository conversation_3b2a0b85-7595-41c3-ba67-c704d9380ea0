import Utilisateur from '../models/utilisateur.js';
import Employe from '../models/Employe.js';
import Departement from '../models/departement.js';
import bcrypt from 'bcrypt';

// Dashboard - Statistiques générales
const getDashboardStats = async (req, res) => {
    try {
        const totalUsers = await Utilisateur.countDocuments();
        const activeUsers = await Utilisateur.countDocuments({ actif: true });
        const inactiveUsers = await Utilisateur.countDocuments({ actif: false });
        
        const usersByRole = await Utilisateur.aggregate([
            { $group: { _id: "$role", count: { $sum: 1 } } }
        ]);

        const totalEmployees = await Employe.countDocuments();
        const totalDepartments = await Departement.countDocuments();

        // Dernières connexions
        const recentLogins = await Utilisateur.find({ derniere_connexion: { $exists: true } })
            .sort({ derniere_connexion: -1 })
            .limit(5)
            .select('email role derniere_connexion');

        res.status(200).json({
            success: true,
            data: {
                users: {
                    total: totalUsers,
                    active: activeUsers,
                    inactive: inactiveUsers,
                    byRole: usersByRole
                },
                employees: totalEmployees,
                departments: totalDepartments,
                recentLogins
            }
        });
    } catch (error) {
        console.error('Erreur getDashboardStats:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la récupération des statistiques"
        });
    }
};

// Gestion des utilisateurs
const getAllUsers = async (req, res) => {
    try {
        const { page = 1, limit = 10, role, status } = req.query;
        
        let filter = {};
        if (role) filter.role = role;
        if (status) filter.actif = status === 'active';

        const users = await Utilisateur.find(filter)
            .select('-mot_de_passe')
            .sort({ createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Utilisateur.countDocuments(filter);

        res.status(200).json({
            success: true,
            data: {
                users,
                pagination: {
                    current: page,
                    pages: Math.ceil(total / limit),
                    total
                }
            }
        });
    } catch (error) {
        console.error('Erreur getAllUsers:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la récupération des utilisateurs"
        });
    }
};

const createUser = async (req, res) => {
    try {
        const { email, password, role, permissions } = req.body;

        // Vérifications
        if (!email || !password || !role) {
            return res.status(400).json({
                success: false,
                message: "Email, mot de passe et rôle sont requis"
            });
        }

        // Vérifier si l'utilisateur existe déjà
        const existingUser = await Utilisateur.findOne({ email });
        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: "Un utilisateur avec cet email existe déjà"
            });
        }

        // Hasher le mot de passe
        const hashedPassword = await bcrypt.hash(password, 10);

        // Créer l'utilisateur
        const newUser = new Utilisateur({
            email,
            mot_de_passe: hashedPassword,
            role,
            permissions: permissions || [],
            actif: true
        });

        await newUser.save();

        // Retourner l'utilisateur sans le mot de passe
        const userResponse = await Utilisateur.findById(newUser._id).select('-mot_de_passe');

        res.status(201).json({
            success: true,
            message: "Utilisateur créé avec succès",
            data: userResponse
        });
    } catch (error) {
        console.error('Erreur createUser:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la création de l'utilisateur"
        });
    }
};

const updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const { email, role, permissions, actif } = req.body;

        // Vérifier que l'utilisateur existe
        const user = await Utilisateur.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "Utilisateur non trouvé"
            });
        }

        // Empêcher la modification de son propre compte
        if (user._id.toString() === req.utilisateur._id.toString()) {
            return res.status(400).json({
                success: false,
                message: "Vous ne pouvez pas modifier votre propre compte"
            });
        }

        // Mettre à jour les champs
        if (email) user.email = email;
        if (role) user.role = role;
        if (permissions) user.permissions = permissions;
        if (typeof actif === 'boolean') user.actif = actif;

        await user.save();

        const updatedUser = await Utilisateur.findById(id).select('-mot_de_passe');

        res.status(200).json({
            success: true,
            message: "Utilisateur mis à jour avec succès",
            data: updatedUser
        });
    } catch (error) {
        console.error('Erreur updateUser:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la mise à jour de l'utilisateur"
        });
    }
};

const deleteUser = async (req, res) => {
    try {
        const { id } = req.params;

        // Vérifier que l'utilisateur existe
        const user = await Utilisateur.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "Utilisateur non trouvé"
            });
        }

        // Empêcher la suppression de son propre compte
        if (user._id.toString() === req.utilisateur._id.toString()) {
            return res.status(400).json({
                success: false,
                message: "Vous ne pouvez pas supprimer votre propre compte"
            });
        }

        await Utilisateur.findByIdAndDelete(id);

        res.status(200).json({
            success: true,
            message: "Utilisateur supprimé avec succès"
        });
    } catch (error) {
        console.error('Erreur deleteUser:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la suppression de l'utilisateur"
        });
    }
};

const resetUserPassword = async (req, res) => {
    try {
        const { id } = req.params;
        const { newPassword } = req.body;

        if (!newPassword) {
            return res.status(400).json({
                success: false,
                message: "Nouveau mot de passe requis"
            });
        }

        const user = await Utilisateur.findById(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "Utilisateur non trouvé"
            });
        }

        const hashedPassword = await bcrypt.hash(newPassword, 10);
        user.mot_de_passe = hashedPassword;
        await user.save();

        res.status(200).json({
            success: true,
            message: "Mot de passe réinitialisé avec succès"
        });
    } catch (error) {
        console.error('Erreur resetUserPassword:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la réinitialisation du mot de passe"
        });
    }
};

// Logs système (simulation)
const getSystemLogs = async (req, res) => {
    try {
        // Simulation de logs - dans un vrai projet, vous récupéreriez les vrais logs
        const logs = [
            {
                id: 1,
                timestamp: new Date(),
                level: 'INFO',
                message: 'Utilisateur <EMAIL> connecté',
                source: 'AUTH'
            },
            {
                id: 2,
                timestamp: new Date(Date.now() - 300000),
                level: 'WARNING',
                message: 'Tentative de connexion échoué<NAME_EMAIL>',
                source: 'AUTH'
            },
            {
                id: 3,
                timestamp: new Date(Date.now() - 600000),
                level: 'INFO',
                message: 'Sauvegarde automatique effectuée',
                source: 'SYSTEM'
            }
        ];

        res.status(200).json({
            success: true,
            data: logs
        });
    } catch (error) {
        console.error('Erreur getSystemLogs:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la récupération des logs"
        });
    }
};

export {
    getDashboardStats,
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    resetUserPassword,
    getSystemLogs
};

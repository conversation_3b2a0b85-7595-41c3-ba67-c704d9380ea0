/**
 * Contrôleur pour la gestion des projets
 */
import Projet from '../models/Projet.js';
import Tache from '../models/Tache.js';
import Archive from '../models/Archive.js';
import Employe from '../models/Employe.js';
import { successResponse, errorResponse, notFoundResponse, serverErrorResponse } from '../utils/responseHelper.js';

/**
 * Récupérer tous les projets
 */
export const getAllProjets = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            statut,
            priorite,
            chef_projet,
            departement,
            archive = false,
            search
        } = req.query;

        // Construire le filtre
        const filter = { archive: archive === 'true' };

        if (statut) filter.statut = statut;
        if (priorite) filter.priorite = priorite;
        if (chef_projet) filter.chef_projet = chef_projet;
        if (departement) filter.departement = departement;

        // Recherche textuelle
        if (search) {
            filter.$or = [
                { nom_projet: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { client: { $regex: search, $options: 'i' } }
            ];
        }

        const projets = await Projet.find(filter)
            .populate('chef_projet', 'nom prenom email poste')
            .populate('departement', 'nom_departement')
            .populate('taches', 'nom_tache statut progression')
            .sort({ date_debut: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Projet.countDocuments(filter);

        // Calculer les statistiques pour chaque projet
        const projetsAvecStats = projets.map(projet => {
            const projetObj = projet.toObject();
            projetObj.estEnRetard = projet.estEnRetard();
            projetObj.joursRestants = projet.joursRestants();
            projetObj.dureeProjet = projet.dureeProjet();
            projetObj.budgetRestant = projet.budgetRestant();

            // Calculer la progression basée sur les tâches
            if (projetObj.taches && projetObj.taches.length > 0) {
                const progressionMoyenne = projetObj.taches.reduce((sum, tache) =>
                    sum + (tache.progression || 0), 0) / projetObj.taches.length;
                projetObj.progressionCalculee = Math.round(progressionMoyenne);
            } else {
                projetObj.progressionCalculee = projetObj.progression || 0;
            }

            return projetObj;
        });

        return successResponse(res, {
            projets: projetsAvecStats,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total
            }
        }, "Projets récupérés avec succès");

    } catch (error) {
        console.error('Erreur getAllProjets:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des projets", error);
    }
};

/**
 * Récupérer un projet par ID avec ses tâches
 */
export const getProjetById = async (req, res) => {
    try {
        const { id } = req.params;

        const projet = await Projet.findById(id)
            .populate('chef_projet', 'nom prenom email poste departement')
            .populate('departement', 'nom_departement description')
            .populate('equipe.employe', 'nom prenom email poste')
            .populate({
                path: 'taches',
                populate: {
                    path: 'employe_assigne',
                    select: 'nom prenom email'
                }
            });

        if (!projet) {
            return notFoundResponse(res, "Projet non trouvé");
        }

        const projetObj = projet.toObject();
        projetObj.estEnRetard = projet.estEnRetard();
        projetObj.joursRestants = projet.joursRestants();
        projetObj.dureeProjet = projet.dureeProjet();
        projetObj.budgetRestant = projet.budgetRestant();

        return successResponse(res, { projet: projetObj }, "Projet récupéré avec succès");

    } catch (error) {
        console.error('Erreur getProjetById:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération du projet", error);
    }
};

/**
 * Créer un nouveau projet
 */
export const createProjet = async (req, res) => {
    try {
        const {
            nom_projet,
            description,
            date_debut,
            date_fin_prevue,
            chef_projet,
            priorite,
            budget_alloue,
            client,
            departement,
            equipe,
            taches
        } = req.body;

        // Validation des champs requis
        if (!nom_projet || !description || !date_debut || !date_fin_prevue || !chef_projet) {
            return errorResponse(res, "Nom, description, dates et chef de projet sont requis", 400);
        }

        // Vérifier que le chef de projet existe
        const chefProjetExists = await Employe.findById(chef_projet);
        if (!chefProjetExists) {
            return notFoundResponse(res, "Chef de projet non trouvé");
        }

        // Validation des dates
        const dateDebut = new Date(date_debut);
        const dateFinPrevue = new Date(date_fin_prevue);

        if (dateFinPrevue <= dateDebut) {
            return errorResponse(res, "La date de fin prévue doit être postérieure à la date de début", 400);
        }

        // Créer le nouveau projet
        const newProjet = new Projet({
            nom_projet: nom_projet.trim(),
            description: description.trim(),
            date_debut: dateDebut,
            date_fin_prevue: dateFinPrevue,
            chef_projet,
            priorite: priorite || 'Moyenne',
            budget_alloue: parseFloat(budget_alloue) || 0,
            client: client?.trim(),
            departement,
            equipe: equipe || []
        });

        await newProjet.save();

        // Créer les tâches si fournies
        if (taches && Array.isArray(taches) && taches.length > 0) {
            const tachesCreees = [];

            for (const tacheData of taches) {
                if (tacheData.nom_tache && tacheData.employe_assigne) {
                    const nouvelleTache = new Tache({
                        ...tacheData,
                        projet: newProjet._id,
                        date_debut: tacheData.date_debut || dateDebut,
                        date_fin_prevue: tacheData.date_fin_prevue || dateFinPrevue
                    });

                    await nouvelleTache.save();
                    tachesCreees.push(nouvelleTache._id);
                }
            }

            // Mettre à jour le projet avec les tâches
            newProjet.taches = tachesCreees;
            await newProjet.save();
        }

        // Récupérer le projet avec les données populées
        const populatedProjet = await Projet.findById(newProjet._id)
            .populate('chef_projet', 'nom prenom email')
            .populate('departement', 'nom_departement')
            .populate('taches', 'nom_tache statut');

        return successResponse(res, { projet: populatedProjet }, "Projet créé avec succès", 201);

    } catch (error) {
        console.error('Erreur createProjet:', error);
        return serverErrorResponse(res, "Erreur lors de la création du projet", error);
    }
};

/**
 * Mettre à jour un projet
 */
export const updateProjet = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        const projet = await Projet.findById(id);
        if (!projet) {
            return notFoundResponse(res, "Projet non trouvé");
        }

        // Vérifier le chef de projet si fourni
        if (updateData.chef_projet && updateData.chef_projet !== projet.chef_projet.toString()) {
            const chefProjetExists = await Employe.findById(updateData.chef_projet);
            if (!chefProjetExists) {
                return notFoundResponse(res, "Chef de projet non trouvé");
            }
        }

        // Validation des dates si fournies
        if (updateData.date_debut || updateData.date_fin_prevue) {
            const dateDebut = updateData.date_debut ? new Date(updateData.date_debut) : projet.date_debut;
            const dateFinPrevue = updateData.date_fin_prevue ? new Date(updateData.date_fin_prevue) : projet.date_fin_prevue;

            if (dateFinPrevue <= dateDebut) {
                return errorResponse(res, "La date de fin prévue doit être postérieure à la date de début", 400);
            }
        }

        // Mettre à jour les champs
        Object.keys(updateData).forEach(key => {
            if (updateData[key] !== undefined && key !== '_id') {
                projet[key] = updateData[key];
            }
        });

        await projet.save();

        // Récupérer le projet mis à jour avec les données populées
        const updatedProjet = await Projet.findById(id)
            .populate('chef_projet', 'nom prenom email')
            .populate('departement', 'nom_departement')
            .populate('taches', 'nom_tache statut progression');

        return successResponse(res, { projet: updatedProjet }, "Projet mis à jour avec succès");

    } catch (error) {
        console.error('Erreur updateProjet:', error);
        return serverErrorResponse(res, "Erreur lors de la mise à jour du projet", error);
    }
};

/**
 * Supprimer un projet
 */
export const deleteProjet = async (req, res) => {
    try {
        const { id } = req.params;

        const projet = await Projet.findById(id).populate('taches');
        if (!projet) {
            return notFoundResponse(res, "Projet non trouvé");
        }

        // Vérifier si le projet peut être supprimé
        if (projet.statut === 'En cours' && projet.taches.some(tache => tache.statut === 'En cours')) {
            return errorResponse(res,
                "Impossible de supprimer un projet en cours avec des tâches actives. Veuillez d'abord terminer ou annuler les tâches.",
                400
            );
        }

        // Supprimer toutes les tâches associées
        await Tache.deleteMany({ projet: id });

        // Supprimer le projet
        await Projet.findByIdAndDelete(id);

        return successResponse(res, null, "Projet supprimé avec succès");

    } catch (error) {
        console.error('Erreur deleteProjet:', error);
        return serverErrorResponse(res, "Erreur lors de la suppression du projet", error);
    }
};

/**
 * Archiver un projet
 */
export const archiverProjet = async (req, res) => {
    try {
        const { id } = req.params;
        const { raison_archivage, description_archivage } = req.body;

        const projet = await Projet.findById(id);
        if (!projet) {
            return notFoundResponse(res, "Projet non trouvé");
        }

        // Récupérer les tâches associées séparément
        const taches = await Tache.find({ projet: id });

        // Créer l'archive
        const archive = new Archive({
            type_element: 'Projet',
            element_id: projet._id,
            donnees_originales: projet.toObject(),
            raison_archivage: raison_archivage || 'Demande utilisateur',
            description_archivage,
            archive_par: req.user._id || req.utilisateur._id,
            date_element_original: projet.createdAt,
            elements_lies: taches.map(tache => tache._id)
        });

        await archive.save();

        // Marquer le projet comme archivé
        projet.archive = true;
        await projet.save();

        return successResponse(res, { archive }, "Projet archivé avec succès");

    } catch (error) {
        console.error('Erreur archiverProjet:', error);
        return serverErrorResponse(res, "Erreur lors de l'archivage du projet", error);
    }
};

/**
 * Obtenir les statistiques des projets
 */
export const getProjetStats = async (req, res) => {
    try {
        const totalProjets = await Projet.countDocuments({ archive: false });
        const projetsActifs = await Projet.countDocuments({ statut: 'En cours', archive: false });
        const projetsTermines = await Projet.countDocuments({ statut: 'Terminé', archive: false });

        // Projets par statut
        const projetsParStatut = await Projet.aggregate([
            { $match: { archive: false } },
            {
                $group: {
                    _id: '$statut',
                    count: { $sum: 1 },
                    budgetTotal: { $sum: '$budget_alloue' },
                    budgetUtilise: { $sum: '$budget_utilise' }
                }
            }
        ]);

        // Projets par priorité
        const projetsParPriorite = await Projet.aggregate([
            { $match: { archive: false } },
            {
                $group: {
                    _id: '$priorite',
                    count: { $sum: 1 }
                }
            }
        ]);

        // Projets en retard
        const projetsEnRetard = await Projet.countDocuments({
            archive: false,
            statut: { $nin: ['Terminé', 'Annulé'] },
            date_fin_prevue: { $lt: new Date() }
        });

        // Budget total
        const budgetStats = await Projet.aggregate([
            { $match: { archive: false } },
            {
                $group: {
                    _id: null,
                    budgetTotalAlloue: { $sum: '$budget_alloue' },
                    budgetTotalUtilise: { $sum: '$budget_utilise' }
                }
            }
        ]);

        return successResponse(res, {
            total: totalProjets,
            actifs: projetsActifs,
            termines: projetsTermines,
            enRetard: projetsEnRetard,
            parStatut: projetsParStatut,
            parPriorite: projetsParPriorite,
            budget: budgetStats[0] || { budgetTotalAlloue: 0, budgetTotalUtilise: 0 }
        }, "Statistiques des projets récupérées avec succès");

    } catch (error) {
        console.error('Erreur getProjetStats:', error);
        return serverErrorResponse(res, "Erreur lors de la récupération des statistiques", error);
    }
};

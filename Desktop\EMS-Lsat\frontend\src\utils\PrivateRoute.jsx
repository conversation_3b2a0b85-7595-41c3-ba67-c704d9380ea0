
import { useAuth } from '../context/authContext.jsx';
import { Navigate } from 'react-router-dom'; // Assurez-vous d'importer navigate depuis react-router-dom

const PrivateRoute = ({children}) => {
  const {utilisateur, loading} = useAuth();

  if (loading) {
    return <div>Loading...</div>; // Affichez un indicateur de chargement pendant que l'état d'authentification est vérifié
  }

  return utilisateur ? children : <Navigate to="/login" />; // Redirigez vers la page de connexion si l'utilisateur n'est pas authentifié
}

export default PrivateRoute;
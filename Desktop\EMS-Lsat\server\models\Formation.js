import mongoose from "mongoose";

const formationSchema = new mongoose.Schema({
  // Informations de base
  nom_formation: {
    type: String,
    required: true,
    trim: true
  },
  organisme: {
    type: String,
    required: true,
    trim: true
  },
  date_debut: {
    type: Date,
    required: true
  },
  date_fin: {
    type: Date,
    required: true
  },
  description: {
    type: String,
    required: true
  },

  // Type et modalités
  type: {
    type: String,
    enum: ['interne', 'externe'],
    default: 'externe'
  },
  certifiante: {
    type: Boolean,
    default: false
  },
  modalite: {
    type: String,
    enum: ['presentielle', 'en_ligne', 'mixte'],
    default: 'presentielle'
  },

  // Lieu et logistique
  lieu: {
    type: String,
    trim: true
  },
  lien_connexion: {
    type: String,
    trim: true
  },

  // Capacité et statut
  capacite_max: {
    type: Number,
    default: 20
  },
  statut: {
    type: String,
    enum: ['planifiee', 'en_cours', 'terminee', 'annulee'],
    default: 'planifiee'
  },

  // Documents associés
  documents: [{
    nom: String,
    type: {
      type: String,
      enum: ['programme', 'support', 'certificat_modele', 'autre']
    },
    url: String,
    date_upload: {
      type: Date,
      default: Date.now
    }
  }],

  // Métadonnées
  cree_par: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Utilisateur',
    required: true
  },
  date_creation: {
    type: Date,
    default: Date.now
  },
  derniere_modification: {
    type: Date,
    default: Date.now
  },

  // Historique des modifications
  historique: [{
    action: String,
    date: {
      type: Date,
      default: Date.now
    },
    utilisateur: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Utilisateur'
    },
    details: String
  }]
}, {
  timestamps: true
});

// Index pour améliorer les performances
formationSchema.index({ date_debut: 1 });
formationSchema.index({ organisme: 1 });
formationSchema.index({ statut: 1 });
formationSchema.index({ type: 1 });

// Middleware pour mettre à jour la date de modification
formationSchema.pre('save', function(next) {
  this.derniere_modification = new Date();
  next();
});

export default mongoose.model("Formation", formationSchema);

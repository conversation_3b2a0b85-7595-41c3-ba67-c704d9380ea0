import mongoose from 'mongoose';

const congeSchema = new mongoose.Schema({
  employe: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "employes",
    required: true
  },
  type_conge: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "typeconges",
    required: true
  },
  date_debut: {
    type: Date,
    required: true
  },
  date_fin: {
    type: Date,
    required: true
  },
  jours_non_ouvrables: {
    type: Number,
    required: true,
    min: 1
  },
  statut: {
    type: String,
    enum: ["en attente", "validé", "refusé"],
    default: "en attente"
  },
  commentaire: {
    type: String,
    trim: true
  },
  commentaire_admin: {
    type: String,
    trim: true
  },
  solde_avant: {
    type: Number,
    default: 0
  },
  solde_apres: {
    type: Number,
    default: 0
  },
  motif_refus: {
    type: String,
    trim: true
  },
  date_traitement: Date,
  traite_par: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "utilisateurs"
  }
}, {
  timestamps: true,
  collection: 'conges'
});

// Index pour optimiser les requêtes
congeSchema.index({ employe: 1, date_debut: 1 });
congeSchema.index({ statut: 1 });
congeSchema.index({ type_conge: 1 });

export default mongoose.model("Conge", congeSchema);

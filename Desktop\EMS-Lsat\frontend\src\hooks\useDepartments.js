/**
 * Hook personnalisé pour la gestion des départements
 */
import { useState, useEffect, useCallback } from 'react';
import { departmentService } from '../services/departmentService';

export const useDepartments = () => {
    const [departments, setDepartments] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({
        current: 1,
        pages: 1,
        total: 0
    });

    const fetchDepartments = useCallback(async (params = {}) => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await departmentService.getAllDepartments(params);
            if (response.success) {
                setDepartments(response.data || []);
                setPagination(response.pagination || { current: 1, pages: 1, total: 0 });
            } else {
                setError(response.message || 'Erreur lors du chargement des départements');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des départements');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchDepartments();
    }, [fetchDepartments]);

    const createDepartment = async (departmentData) => {
        try {
            const response = await departmentService.createDepartment(departmentData);
            if (response.success) {
                await fetchDepartments(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la création');
            }
        } catch (error) {
            throw error;
        }
    };

    const updateDepartment = async (id, departmentData) => {
        try {
            const response = await departmentService.updateDepartment(id, departmentData);
            if (response.success) {
                await fetchDepartments(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la mise à jour');
            }
        } catch (error) {
            throw error;
        }
    };

    const deleteDepartment = async (id) => {
        try {
            const response = await departmentService.deleteDepartment(id);
            if (response.success) {
                await fetchDepartments(); // Recharger la liste
                return response;
            } else {
                throw new Error(response.message || 'Erreur lors de la suppression');
            }
        } catch (error) {
            throw error;
        }
    };

    return {
        departments,
        loading,
        error,
        pagination,
        fetchDepartments,
        createDepartment,
        updateDepartment,
        deleteDepartment
    };
};

/**
 * Hook pour obtenir un département spécifique
 */
export const useDepartment = (id) => {
    const [department, setDepartment] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchDepartment = useCallback(async () => {
        if (!id) return;
        
        setLoading(true);
        setError(null);
        
        try {
            const response = await departmentService.getDepartmentById(id);
            if (response.success) {
                setDepartment(response.data);
            } else {
                setError(response.message || 'Département non trouvé');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement du département');
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        fetchDepartment();
    }, [fetchDepartment]);

    return { department, loading, error, refetch: fetchDepartment };
};

/**
 * Hook pour les statistiques des départements
 */
export const useDepartmentStats = () => {
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchStats = useCallback(async () => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await departmentService.getDepartmentStats();
            if (response.success) {
                setStats(response.data);
            } else {
                setError(response.message || 'Erreur lors du chargement des statistiques');
            }
        } catch (error) {
            setError(error.message || 'Erreur lors du chargement des statistiques');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchStats();
    }, [fetchStats]);

    return { stats, loading, error, refetch: fetchStats };
};

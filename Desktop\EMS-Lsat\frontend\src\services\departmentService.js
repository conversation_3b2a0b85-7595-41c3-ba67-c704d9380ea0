/**
 * Service pour la gestion des départements
 */
import api from './api';

export const departmentService = {
    /**
     * Obtenir tous les départements
     * @param {Object} params - Paramètres de pagination
     * @returns {Promise} Liste des départements
     */
    getAllDepartments: async (params = {}) => {
        try {
            const { page = 1, limit = 10 } = params;
            const response = await api.get(`/departments?page=${page}&limit=${limit}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des départements' };
        }
    },

    /**
     * Obtenir un département par ID
     * @param {string} id - ID du département
     * @returns {Promise} Données du département
     */
    getDepartmentById: async (id) => {
        try {
            const response = await api.get(`/departments/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération du département' };
        }
    },

    /**
     * Créer un nouveau département
     * @param {Object} departmentData - Données du département
     * @returns {Promise} Département créé
     */
    createDepartment: async (departmentData) => {
        try {
            const response = await api.post('/departments', departmentData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la création du département' };
        }
    },

    /**
     * Mettre à jour un département
     * @param {string} id - ID du département
     * @param {Object} departmentData - Nouvelles données du département
     * @returns {Promise} Département mis à jour
     */
    updateDepartment: async (id, departmentData) => {
        try {
            const response = await api.put(`/departments/${id}`, departmentData);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la mise à jour du département' };
        }
    },

    /**
     * Supprimer un département
     * @param {string} id - ID du département
     * @returns {Promise} Confirmation de suppression
     */
    deleteDepartment: async (id) => {
        try {
            const response = await api.delete(`/departments/${id}`);
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la suppression du département' };
        }
    },

    /**
     * Obtenir les statistiques des départements
     * @returns {Promise} Statistiques
     */
    getDepartmentStats: async () => {
        try {
            const response = await api.get('/departments/stats/overview');
            return response.data;
        } catch (error) {
            throw error.response?.data || { message: 'Erreur lors de la récupération des statistiques' };
        }
    }
};

import Departement from '../models/departement.js';

// Récupérer tous les départements
const getAllDepartments = async (req, res) => {
    try {
        const departments = await Departement.find()
            .populate('responsable', 'nom prenom email poste')
            .sort({ nom_departement: 1 });

        res.status(200).json({
            success: true,
            data: departments
        });
    } catch (error) {
        console.error('Erreur getAllDepartments:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la récupération des départements"
        });
    }
};

// Récupérer un département par ID
const getDepartmentById = async (req, res) => {
    try {
        const { id } = req.params;

        const department = await Departement.findById(id)
            .populate('responsable', 'nom prenom email poste');

        if (!department) {
            return res.status(404).json({
                success: false,
                message: "Département non trouvé"
            });
        }

        res.status(200).json({
            success: true,
            data: department
        });
    } catch (error) {
        console.error('Erreur getDepartmentById:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la récupération du département"
        });
    }
};

// Créer un nouveau département
const createDepartment = async (req, res) => {
    try {
        const { nom_departement, description, responsable, is_active } = req.body;

        // Vérifications obligatoires
        if (!nom_departement) {
            return res.status(400).json({
                success: false,
                message: "Le nom du département est requis"
            });
        }

        // Vérifier si le département existe déjà
        const existingDepartment = await Departement.findOne({ nom_departement });
        if (existingDepartment) {
            return res.status(400).json({
                success: false,
                message: "Un département avec ce nom existe déjà"
            });
        }

        // Vérifier que le responsable existe si fourni
        if (responsable) {
            const Employe = (await import('../models/Employe.js')).default;
            const employeExists = await Employe.findById(responsable);
            if (!employeExists) {
                return res.status(400).json({
                    success: false,
                    message: "L'employé responsable spécifié n'existe pas"
                });
            }
        }

        // Créer le département
        const newDepartment = new Departement({
            nom_departement,
            description,
            responsable: responsable || null,
            is_active: is_active !== undefined ? is_active : true
        });

        await newDepartment.save();

        // Récupérer le département créé avec les relations
        const populatedDepartment = await Departement.findById(newDepartment._id)
            .populate('responsable', 'nom prenom email poste');

        res.status(201).json({
            success: true,
            message: "Département créé avec succès",
            data: populatedDepartment
        });
    } catch (error) {
        console.error('Erreur createDepartment:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la création du département"
        });
    }
};

// Mettre à jour un département
const updateDepartment = async (req, res) => {
    try {
        console.log('🔄 Mise à jour du département:', req.params.id);
        console.log('📝 Données reçues:', req.body);

        const { id } = req.params;
        const { nom_departement, description, responsable, is_active } = req.body;

        const department = await Departement.findById(id);
        if (!department) {
            return res.status(404).json({
                success: false,
                message: "Département non trouvé"
            });
        }

        console.log('📋 Département avant modification:', {
            nom: department.nom_departement,
            description: department.description,
            responsable: department.responsable,
            is_active: department.is_active
        });

        // Vérifier si le nouveau nom existe déjà (sauf pour le département actuel)
        if (nom_departement && nom_departement !== department.nom_departement) {
            const existingDepartment = await Departement.findOne({ nom_departement });
            if (existingDepartment) {
                return res.status(400).json({
                    success: false,
                    message: "Un département avec ce nom existe déjà"
                });
            }
        }

        // Vérifier que le responsable existe si fourni
        if (responsable) {
            const Employe = (await import('../models/Employe.js')).default;
            const employeExists = await Employe.findById(responsable);
            if (!employeExists) {
                return res.status(400).json({
                    success: false,
                    message: "L'employé responsable spécifié n'existe pas"
                });
            }
        }

        // Mettre à jour les champs
        console.log('🔧 Mise à jour des champs...');
        if (nom_departement) {
            console.log('📝 Nom:', nom_departement);
            department.nom_departement = nom_departement;
        }
        if (description !== undefined) {
            console.log('📝 Description:', description);
            department.description = description;
        }
        if (responsable !== undefined) {
            console.log('📝 Responsable:', responsable);
            department.responsable = responsable || null;
        }
        if (is_active !== undefined) {
            console.log('📝 is_active:', is_active, '(type:', typeof is_active, ')');
            department.is_active = is_active;
        }

        console.log('📋 Département après modification (avant save):', {
            nom: department.nom_departement,
            description: department.description,
            responsable: department.responsable,
            is_active: department.is_active
        });

        await department.save();
        console.log('💾 Département sauvegardé');

        // Récupérer le département mis à jour avec les relations
        const updatedDepartment = await Departement.findById(id)
            .populate('responsable', 'nom prenom email poste');

        console.log('✅ Département final:', {
            nom: updatedDepartment.nom_departement,
            description: updatedDepartment.description,
            responsable: updatedDepartment.responsable,
            is_active: updatedDepartment.is_active
        });

        res.status(200).json({
            success: true,
            message: "Département mis à jour avec succès",
            data: updatedDepartment
        });
    } catch (error) {
        console.error('Erreur updateDepartment:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la mise à jour du département"
        });
    }
};

// Supprimer un département
const deleteDepartment = async (req, res) => {
    try {
        console.log('🗑️ Tentative de suppression du département:', req.params.id);

        const { id } = req.params;

        const department = await Departement.findById(id);
        if (!department) {
            console.log('❌ Département non trouvé:', id);
            return res.status(404).json({
                success: false,
                message: "Département non trouvé"
            });
        }

        console.log('📋 Département trouvé:', department.nom_departement);

        // Vérifier s'il y a des employés dans ce département
        const Employe = (await import('../models/Employe.js')).default;
        console.log('🔍 Vérification des employés dans le département...');

        const employeesCount = await Employe.countDocuments({ departement: id });
        console.log('👥 Nombre d\'employés trouvés:', employeesCount);

        if (employeesCount > 0) {
            console.log('⚠️ Suppression bloquée - employés assignés');
            return res.status(400).json({
                success: false,
                message: `Impossible de supprimer le département "${department.nom_departement}". ${employeesCount} employé(s) y sont assigné(s).`
            });
        }

        // Vérifier s'il y a des services dans ce département
        const Service = (await import('../models/Service.js')).default;
        const servicesCount = await Service.countDocuments({ departement: id });
        console.log('🏢 Nombre de services trouvés:', servicesCount);

        if (servicesCount > 0) {
            console.log('⚠️ Suppression bloquée - services assignés');
            return res.status(400).json({
                success: false,
                message: `Impossible de supprimer le département "${department.nom_departement}". ${servicesCount} service(s) y sont rattaché(s).`
            });
        }

        console.log('✅ Suppression autorisée - aucun employé ni service');
        await Departement.findByIdAndDelete(id);

        console.log('🎉 Département supprimé avec succès');
        res.status(200).json({
            success: true,
            message: "Département supprimé avec succès"
        });
    } catch (error) {
        console.error('❌ Erreur deleteDepartment:', error);
        console.error('❌ Stack trace:', error.stack);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la suppression du département",
            error: error.message
        });
    }
};

// Statistiques des départements
const getDepartmentStats = async (req, res) => {
    try {
        const totalDepartments = await Departement.countDocuments();

        // Compter les employés par département
        const Employe = (await import('../models/Employe.js')).default;
        const employeesByDepartment = await Employe.aggregate([
            {
                $lookup: {
                    from: 'departements',
                    localField: 'departement',
                    foreignField: '_id',
                    as: 'dept'
                }
            },
            {
                $group: {
                    _id: '$departement',
                    count: { $sum: 1 },
                    departmentName: { $first: '$dept.nom_departement' }
                }
            },
            {
                $sort: { count: -1 }
            }
        ]);

        res.status(200).json({
            success: true,
            data: {
                total: totalDepartments,
                employeesByDepartment
            }
        });
    } catch (error) {
        console.error('Erreur getDepartmentStats:', error);
        res.status(500).json({
            success: false,
            message: "Erreur lors de la récupération des statistiques"
        });
    }
};

export {
    getAllDepartments,
    getDepartmentById,
    createDepartment,
    updateDepartment,
    deleteDepartment,
    getDepartmentStats
};

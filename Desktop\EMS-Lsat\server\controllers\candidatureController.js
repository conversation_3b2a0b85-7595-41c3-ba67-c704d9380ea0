import CandidatureFormation from '../models/CandidatureFormation.js';
import Formation from '../models/Formation.js';
import Employe from '../models/Employe.js';

// 📝 Postuler à une formation (employé)
const postulerFormation = async (req, res) => {
  try {
    const { id: formationId } = req.params;
    const { employe_id, message_employe } = req.body;
    const utilisateurId = req.utilisateur._id;

    console.log('Candidature - Formation ID:', formationId);
    console.log('Candidature - Employé ID:', employe_id);
    console.log('Candidature - Utilisateur connecté:', utilisateurId);

    // Vérifier que la formation existe
    const formation = await Formation.findById(formationId);
    if (!formation) {
      return res.status(404).json({
        success: false,
        message: 'Formation non trouvée'
      });
    }

    // Vérifier que l'employé existe
    const employe = await Employe.findById(employe_id);
    if (!employe) {
      return res.status(404).json({
        success: false,
        message: 'Employé non trouvé'
      });
    }

    // Vérifier que l'employé connecté correspond à celui qui postule
    if (employe.utilisateur.toString() !== utilisateurId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Vous ne pouvez postuler que pour votre propre compte'
      });
    }

    // Vérifier si l'employé a déjà postulé
    const candidatureExistante = await CandidatureFormation.findOne({
      formation_id: formationId,
      employe_id: employe_id
    });

    if (candidatureExistante) {
      return res.status(400).json({
        success: false,
        message: 'Vous avez déjà postulé à cette formation'
      });
    }

    // Créer la candidature
    const candidature = new CandidatureFormation({
      formation_id: formationId,
      employe_id: employe_id,
      message_employe: message_employe || '',
      statut: 'en_attente',
      vue_par_rh: false,
      notification_employe_vue: true // L'employé vient de créer, donc il l'a "vue"
    });

    await candidature.save();

    // Populer les données pour la réponse
    await candidature.populate([
      { path: 'formation_id', select: 'nom_formation organisme date_debut' },
      { path: 'employe_id', select: 'nom prenom email' }
    ]);

    res.status(201).json({
      success: true,
      message: 'Candidature envoyée avec succès',
      candidature
    });

  } catch (error) {
    console.error('Erreur lors de la candidature:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'envoi de la candidature',
      error: error.message
    });
  }
};

// 📋 Obtenir les candidatures pour RH
const getCandidaturesPourRH = async (req, res) => {
  try {
    const { statut, formation_id, page = 1, limit = 20 } = req.query;

    // Construction du filtre
    const filter = {};
    if (statut) filter.statut = statut;
    if (formation_id) filter.formation_id = formation_id;

    const candidatures = await CandidatureFormation.find(filter)
      .populate('formation_id', 'nom_formation organisme date_debut date_fin')
      .populate('employe_id', 'nom prenom email telephone departement')
      .populate('traite_par', 'email role')
      .sort({ date_candidature: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await CandidatureFormation.countDocuments(filter);

    res.json({
      success: true,
      candidatures,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        total,
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des candidatures:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des candidatures',
      error: error.message
    });
  }
};

// ✅ Approuver une candidature (RH)
const approuverCandidature = async (req, res) => {
  try {
    const { id } = req.params;
    const { message_rh } = req.body;
    const rhId = req.utilisateur._id;

    const candidature = await CandidatureFormation.findById(id);
    if (!candidature) {
      return res.status(404).json({
        success: false,
        message: 'Candidature non trouvée'
      });
    }

    if (candidature.statut !== 'en_attente') {
      return res.status(400).json({
        success: false,
        message: 'Cette candidature a déjà été traitée'
      });
    }

    candidature.statut = 'approuvee';
    candidature.date_reponse = new Date();
    candidature.traite_par = rhId;
    candidature.message_rh = message_rh || '';
    candidature.notification_employe_vue = false; // Nouvelle notification pour l'employé

    await candidature.save();

    await candidature.populate([
      { path: 'formation_id', select: 'nom_formation organisme' },
      { path: 'employe_id', select: 'nom prenom email' },
      { path: 'traite_par', select: 'email role' }
    ]);

    res.json({
      success: true,
      message: 'Candidature approuvée avec succès',
      candidature
    });

  } catch (error) {
    console.error('Erreur lors de l\'approbation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'approbation de la candidature',
      error: error.message
    });
  }
};

// ❌ Refuser une candidature (RH)
const refuserCandidature = async (req, res) => {
  try {
    const { id } = req.params;
    const { message_rh } = req.body;
    const rhId = req.utilisateur._id;

    const candidature = await CandidatureFormation.findById(id);
    if (!candidature) {
      return res.status(404).json({
        success: false,
        message: 'Candidature non trouvée'
      });
    }

    if (candidature.statut !== 'en_attente') {
      return res.status(400).json({
        success: false,
        message: 'Cette candidature a déjà été traitée'
      });
    }

    candidature.statut = 'refusee';
    candidature.date_reponse = new Date();
    candidature.traite_par = rhId;
    candidature.message_rh = message_rh || '';
    candidature.notification_employe_vue = false; // Nouvelle notification pour l'employé

    await candidature.save();

    await candidature.populate([
      { path: 'formation_id', select: 'nom_formation organisme' },
      { path: 'employe_id', select: 'nom prenom email' },
      { path: 'traite_par', select: 'email role' }
    ]);

    res.json({
      success: true,
      message: 'Candidature refusée',
      candidature
    });

  } catch (error) {
    console.error('Erreur lors du refus:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du refus de la candidature',
      error: error.message
    });
  }
};

// 🔔 Obtenir les notifications pour employé
const getNotificationsEmploye = async (req, res) => {
  try {
    const utilisateurId = req.utilisateur._id;

    // Trouver l'employé correspondant à l'utilisateur connecté
    const employe = await Employe.findOne({ utilisateur: utilisateurId });
    if (!employe) {
      return res.status(404).json({
        success: false,
        message: 'Employé non trouvé'
      });
    }

    // Récupérer les candidatures avec réponse non vue
    const notifications = await CandidatureFormation.find({
      employe_id: employe._id,
      notification_employe_vue: false,
      statut: { $in: ['approuvee', 'refusee'] }
    })
      .populate('formation_id', 'nom_formation organisme date_debut')
      .populate('traite_par', 'email role')
      .sort({ date_reponse: -1 });

    res.json({
      success: true,
      notifications,
      count: notifications.length
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des notifications:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des notifications',
      error: error.message
    });
  }
};

// 🔔 Obtenir les notifications pour RH (nouvelles candidatures)
const getNotificationsRH = async (req, res) => {
  try {
    // Récupérer les candidatures non vues par RH
    const notifications = await CandidatureFormation.find({
      vue_par_rh: false,
      statut: 'en_attente'
    })
      .populate('formation_id', 'nom_formation organisme date_debut')
      .populate('employe_id', 'nom prenom email departement')
      .sort({ date_candidature: -1 })
      .limit(20); // Limiter à 20 notifications récentes

    res.json({
      success: true,
      notifications,
      count: notifications.length
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des notifications RH:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des notifications',
      error: error.message
    });
  }
};

// 👁️ Marquer les notifications RH comme vues
const marquerNotificationsRHVues = async (req, res) => {
  try {
    const { candidature_ids } = req.body;

    if (candidature_ids && candidature_ids.length > 0) {
      await CandidatureFormation.updateMany(
        { _id: { $in: candidature_ids } },
        { vue_par_rh: true }
      );
    } else {
      // Marquer toutes les notifications non vues comme vues
      await CandidatureFormation.updateMany(
        { vue_par_rh: false, statut: 'en_attente' },
        { vue_par_rh: true }
      );
    }

    res.json({
      success: true,
      message: 'Notifications marquées comme vues'
    });

  } catch (error) {
    console.error('Erreur lors du marquage des notifications:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du marquage des notifications',
      error: error.message
    });
  }
};

// 📋 Obtenir toutes les candidatures d'un employé (pour affichage sur les cartes)
const getMesCandidatures = async (req, res) => {
  try {
    const utilisateurId = req.utilisateur._id;

    // Trouver l'employé correspondant à l'utilisateur connecté
    const employe = await Employe.findOne({ utilisateur: utilisateurId });
    if (!employe) {
      return res.status(404).json({
        success: false,
        message: 'Employé non trouvé'
      });
    }

    // Récupérer toutes les candidatures de cet employé
    const candidatures = await CandidatureFormation.find({
      employe_id: employe._id
    })
      .populate('formation_id', 'nom_formation organisme date_debut date_fin')
      .populate('traite_par', 'email role')
      .sort({ date_candidature: -1 });

    res.json({
      success: true,
      candidatures,
      count: candidatures.length
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des candidatures:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des candidatures',
      error: error.message
    });
  }
};

// 🗑️ Supprimer une candidature (employé seulement)
const supprimerMaCandidature = async (req, res) => {
  try {
    const utilisateurId = req.utilisateur._id;
    const candidatureId = req.params.candidatureId;

    // Trouver l'employé correspondant à l'utilisateur connecté
    const employe = await Employe.findOne({ utilisateur: utilisateurId });
    if (!employe) {
      return res.status(404).json({
        success: false,
        message: 'Employé non trouvé'
      });
    }

    // Vérifier que la candidature existe et appartient à cet employé
    const candidature = await CandidatureFormation.findOne({
      _id: candidatureId,
      employe_id: employe._id
    }).populate('formation_id', 'nom_formation');

    if (!candidature) {
      return res.status(404).json({
        success: false,
        message: 'Candidature non trouvée ou vous n\'êtes pas autorisé à la supprimer'
      });
    }

    // Vérifier que la candidature n'a pas encore été traitée
    if (candidature.statut !== 'en_attente') {
      return res.status(400).json({
        success: false,
        message: `Impossible de supprimer une candidature ${candidature.statut === 'approuvee' ? 'approuvée' : 'refusée'}`
      });
    }

    // Supprimer la candidature
    await CandidatureFormation.findByIdAndDelete(candidatureId);

    console.log(`✅ Candidature supprimée: ${employe.prenom} ${employe.nom} pour ${candidature.formation_id.nom_formation}`);

    res.json({
      success: true,
      message: 'Candidature supprimée avec succès',
      candidature: {
        formation: candidature.formation_id.nom_formation,
        date_candidature: candidature.date_candidature
      }
    });

  } catch (error) {
    console.error('Erreur lors de la suppression de la candidature:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la candidature',
      error: error.message
    });
  }
};

export {
  postulerFormation,
  getCandidaturesPourRH,
  approuverCandidature,
  refuserCandidature,
  getNotificationsEmploye,
  getNotificationsRH,
  marquerNotificationsRHVues,
  getMesCandidatures,
  supprimerMaCandidature
};

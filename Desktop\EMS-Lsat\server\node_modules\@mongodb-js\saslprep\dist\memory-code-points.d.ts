import bitfield from 'sparse-bitfield';
export declare function createMemoryCodePoints(data: <PERSON>uffer): {
    unassigned_code_points: bitfield.BitFieldInstance;
    commonly_mapped_to_nothing: bitfield.BitFieldInstance;
    non_ASCII_space_characters: bitfield.BitFieldInstance;
    prohibited_characters: bitfield.BitFieldInstance;
    bidirectional_r_al: bitfield.BitFieldInstance;
    bidirectional_l: bitfield.BitFieldInstance;
};
//# sourceMappingURL=memory-code-points.d.ts.map
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../context/authContext.jsx';
import { useNavigate } from 'react-router-dom';
import { FaEye, FaEyeSlash, FaUsers, FaTimes } from 'react-icons/fa';

const Login = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [error, setError] = useState(null);
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const { login } = useAuth();
    const navigate = useNavigate();

    useEffect(() => {
        const handleResize = () => {
            setIsLargeScreen(window.innerWidth >= 1024);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);
        setError(null);

        try {
            const response = await axios.post('http://localhost:4000/api/auth/login', { email, password });

            if (response.data.success) {
                login(response.data.utilisateur);
                localStorage.setItem('token', response.data.token);

                if (response.data.utilisateur.role === 'admin') {
                    navigate('/AdminPage');
                } else if (response.data.utilisateur.role === 'rh') {
                    navigate('/RHpage');
                } else {
                    navigate('/employee-page');
                }
            }
        } catch (error) {
            if (error.response && !error.response.data.success) {
                setError(error.response.data.message);
            } else {
                setError("Erreur de connexion au serveur");
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div style={{
            minHeight: '100vh',
            background: 'linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%)',
            position: 'relative',
            overflow: 'hidden'
        }}>
            {/* Background Image with Overlay */}
            <div
                style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundImage: 'url(/Female-human-resource-manager-shaking-hands-with-newly-hired-employees.jpg)',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                    filter: 'brightness(0.3) blur(1px)'
                }}
            />

            {/* Gradient Overlay */}
            <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(88, 28, 135, 0.7) 50%, rgba(15, 23, 42, 0.8) 100%)'
            }} />

            {/* Content */}
            <div style={{
                position: 'relative',
                zIndex: 10,
                minHeight: '100vh',
                display: 'flex'
            }}>
                {/* Login Modal - Full Width */}
                <div style={{
                    width: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '2rem'
                }}>
                    <div style={{
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: '1rem',
                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                        padding: '2rem',
                        width: '100%',
                        maxWidth: '28rem',
                        position: 'relative'
                    }}>
                        {/* Close Button (decorative) */}
                        <button style={{
                            position: 'absolute',
                            top: '1rem',
                            right: '1rem',
                            color: '#9ca3af',
                            background: 'none',
                            border: 'none',
                            cursor: 'pointer',
                            transition: 'color 0.2s'
                        }}>
                            <FaTimes style={{ fontSize: '1.125rem' }} />
                        </button>

                        {/* Logo */}
                        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                            <div style={{
                                width: '4rem',
                                height: '4rem',
                                background: 'linear-gradient(90deg, #3b82f6 0%, #9333ea 100%)',
                                borderRadius: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                margin: '0 auto 1rem auto'
                            }}>
                                <FaUsers style={{ color: 'white', fontSize: '1.5rem' }} />
                            </div>
                            <h2 style={{
                                fontSize: '1.5rem',
                                fontWeight: 'bold',
                                color: '#1f2937',
                                marginBottom: '0.5rem'
                            }}>Connexion/Inscription</h2>
                            <p style={{
                                fontSize: '0.875rem',
                                color: '#6b7280'
                            }}>
                                Après l'inscription, c'est à dire que vous acceptez notre{' '}
                                <span style={{ color: '#3b82f6', cursor: 'pointer' }}>Politique de Confidentialité</span>
                            </p>
                        </div>

                        {/* Error Message */}
                        {error && (
                            <div style={{
                                backgroundColor: '#fef2f2',
                                border: '1px solid #fecaca',
                                color: '#b91c1c',
                                padding: '0.75rem 1rem',
                                borderRadius: '0.5rem',
                                marginBottom: '1.5rem'
                            }}>
                                {error}
                            </div>
                        )}

                        {/* Login Form */}
                        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                            {/* Email Field */}
                            <div>
                                <label htmlFor="email" style={{
                                    display: 'block',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    color: '#374151',
                                    marginBottom: '0.5rem'
                                }}>
                                    E-mail
                                </label>
                                <input
                                    id="email"
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '0.75rem 1rem',
                                        border: '1px solid #d1d5db',
                                        borderRadius: '0.5rem',
                                        fontSize: '1rem',
                                        transition: 'all 0.2s',
                                        outline: 'none'
                                    }}
                                    placeholder="<EMAIL>"
                                    onFocus={(e) => {
                                        e.target.style.borderColor = '#3b82f6';
                                        e.target.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.2)';
                                    }}
                                    onBlur={(e) => {
                                        e.target.style.borderColor = '#d1d5db';
                                        e.target.style.boxShadow = 'none';
                                    }}
                                />
                            </div>

                            {/* Password Field */}
                            <div>
                                <label htmlFor="password" style={{
                                    display: 'block',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    color: '#374151',
                                    marginBottom: '0.5rem'
                                }}>
                                    Mot de Passe
                                </label>
                                <div style={{ position: 'relative' }}>
                                    <input
                                        id="password"
                                        type={showPassword ? "text" : "password"}
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        required
                                        style={{
                                            width: '100%',
                                            padding: '0.75rem 3rem 0.75rem 1rem',
                                            border: '1px solid #d1d5db',
                                            borderRadius: '0.5rem',
                                            fontSize: '1rem',
                                            transition: 'all 0.2s',
                                            outline: 'none'
                                        }}
                                        placeholder="Veuillez saisir le mot de passe"
                                        onFocus={(e) => {
                                            e.target.style.borderColor = '#3b82f6';
                                            e.target.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.2)';
                                        }}
                                        onBlur={(e) => {
                                            e.target.style.borderColor = '#d1d5db';
                                            e.target.style.boxShadow = 'none';
                                        }}
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        style={{
                                            position: 'absolute',
                                            right: '0.75rem',
                                            top: '50%',
                                            transform: 'translateY(-50%)',
                                            color: '#9ca3af',
                                            background: 'none',
                                            border: 'none',
                                            cursor: 'pointer',
                                            transition: 'color 0.2s'
                                        }}
                                    >
                                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                                    </button>
                                </div>
                                <div style={{ textAlign: 'right', marginTop: '0.5rem' }}>
                                    <a href="#" style={{
                                        fontSize: '0.875rem',
                                        color: '#3b82f6',
                                        textDecoration: 'none',
                                        transition: 'color 0.2s'
                                    }}>
                                        Mot de passe oublié ?
                                    </a>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <button
                                type="submit"
                                disabled={isLoading}
                                style={{
                                    width: '100%',
                                    background: isLoading ? '#9ca3af' : 'linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)',
                                    color: 'white',
                                    padding: '0.75rem 1rem',
                                    border: 'none',
                                    borderRadius: '0.5rem',
                                    fontWeight: '500',
                                    fontSize: '1rem',
                                    cursor: isLoading ? 'not-allowed' : 'pointer',
                                    transition: 'all 0.2s',
                                    opacity: isLoading ? 0.5 : 1
                                }}
                                onMouseEnter={(e) => {
                                    if (!isLoading) {
                                        e.target.style.background = 'linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%)';
                                    }
                                }}
                                onMouseLeave={(e) => {
                                    if (!isLoading) {
                                        e.target.style.background = 'linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)';
                                    }
                                }}
                            >
                                {isLoading ? "Connexion..." : "Se connecter"}
                            </button>
                        </form>

                        {/* Footer */}
                        <div style={{ marginTop: '1.5rem', textAlign: 'center' }}>
                            <p style={{
                                fontSize: '0.875rem',
                                color: '#6b7280',
                                marginBottom: '1rem'
                            }}>
                                Avez-vous besoin d'un compte ? <span style={{ color: '#3b82f6', cursor: 'pointer' }}>S'inscrire</span>
                            </p>
                            <div style={{ textAlign: 'center' }}>
                                <span style={{ fontSize: '0.875rem', color: '#9ca3af' }}>OU</span>
                            </div>
                            <button style={{
                                marginTop: '1rem',
                                width: '100%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                padding: '0.75rem 1rem',
                                border: '1px solid #d1d5db',
                                borderRadius: '0.5rem',
                                backgroundColor: 'white',
                                cursor: 'pointer',
                                transition: 'background-color 0.2s'
                            }}
                            onMouseEnter={(e) => e.target.style.backgroundColor = '#f9fafb'}
                            onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
                            >
                                <svg style={{ width: '1.25rem', height: '1.25rem', marginRight: '0.5rem' }} viewBox="0 0 24 24">
                                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                                <span style={{ color: '#374151' }}>Google</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Login
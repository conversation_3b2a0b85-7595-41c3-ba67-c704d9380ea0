import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../context/authContext.jsx';
import { useNavigate } from 'react-router-dom';
import { FaEye, FaEyeSlash, FaUsers, FaBuilding, FaChartLine, FaTimes } from 'react-icons/fa';

const Login = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [error, setError] = useState(null);
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isLargeScreen, setIsLargeScreen] = useState(window.innerWidth >= 1024);
    const { login } = useAuth();
    const navigate = useNavigate();

    useEffect(() => {
        const handleResize = () => {
            setIsLargeScreen(window.innerWidth >= 1024);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);
        setError(null);

        try {
            const response = await axios.post('http://localhost:4000/api/auth/login', { email, password });

            if (response.data.success) {
                login(response.data.utilisateur);
                localStorage.setItem('token', response.data.token);

                if (response.data.utilisateur.role === 'admin') {
                    navigate('/AdminPage');
                } else if (response.data.utilisateur.role === 'rh') {
                    navigate('/RHpage');
                } else {
                    navigate('/employee-page');
                }
            }
        } catch (error) {
            if (error.response && !error.response.data.success) {
                setError(error.response.data.message);
            } else {
                setError("Erreur de connexion au serveur");
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div style={{
            minHeight: '100vh',
            background: 'linear-gradient(135deg, #0f172a 0%, #581c87 50%, #0f172a 100%)',
            position: 'relative',
            overflow: 'hidden'
        }}>
            {/* Background Image with Overlay */}
            <div
                style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundImage: 'url(/Female-human-resource-manager-shaking-hands-with-newly-hired-employees.jpg)',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                    filter: 'brightness(0.3) blur(1px)'
                }}
            />

            {/* Gradient Overlay */}
            <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(88, 28, 135, 0.7) 50%, rgba(15, 23, 42, 0.8) 100%)'
            }} />

            {/* Content */}
            <div style={{
                position: 'relative',
                zIndex: 10,
                minHeight: '100vh',
                display: 'flex'
            }}>
                {/* Left Side - Statistics and Branding */}
                <div style={{
                    display: isLargeScreen ? 'flex' : 'none',
                    width: '50%',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'flex-start',
                    padding: '3rem',
                    color: 'white'
                }}>
                    {/* Logo/Brand */}
                    <div style={{ marginBottom: '3rem' }}>
                        <h1 style={{
                            fontSize: '2.25rem',
                            fontWeight: 'bold',
                            marginBottom: '0.5rem',
                            background: 'linear-gradient(90deg, #60a5fa 0%, #a78bfa 100%)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            backgroundClip: 'text'
                        }}>
                            EMS Platform
                        </h1>
                        <p style={{
                            fontSize: '1.25rem',
                            color: '#d1d5db'
                        }}>Système de Gestion des Employés</p>
                    </div>

                    {/* Statistics Grid */}
                    <div style={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        gap: '2rem',
                        width: '100%',
                        maxWidth: '28rem'
                    }}>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '4rem',
                                height: '4rem',
                                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                                borderRadius: '50%',
                                marginBottom: '1rem',
                                margin: '0 auto 1rem auto'
                            }}>
                                <FaUsers style={{ fontSize: '1.5rem', color: '#60a5fa' }} />
                            </div>
                            <div style={{ fontSize: '1.875rem', fontWeight: 'bold', color: 'white', marginBottom: '0.25rem' }}>500+</div>
                            <div style={{ fontSize: '0.875rem', color: '#d1d5db' }}>Employés</div>
                        </div>

                        <div style={{ textAlign: 'center' }}>
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '4rem',
                                height: '4rem',
                                backgroundColor: 'rgba(168, 85, 247, 0.2)',
                                borderRadius: '50%',
                                marginBottom: '1rem',
                                margin: '0 auto 1rem auto'
                            }}>
                                <FaBuilding style={{ fontSize: '1.5rem', color: '#a78bfa' }} />
                            </div>
                            <div style={{ fontSize: '1.875rem', fontWeight: 'bold', color: 'white', marginBottom: '0.25rem' }}>25+</div>
                            <div style={{ fontSize: '0.875rem', color: '#d1d5db' }}>Départements</div>
                        </div>

                        <div style={{ textAlign: 'center' }}>
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '4rem',
                                height: '4rem',
                                backgroundColor: 'rgba(34, 197, 94, 0.2)',
                                borderRadius: '50%',
                                marginBottom: '1rem',
                                margin: '0 auto 1rem auto'
                            }}>
                                <FaChartLine style={{ fontSize: '1.5rem', color: '#4ade80' }} />
                            </div>
                            <div style={{ fontSize: '1.875rem', fontWeight: 'bold', color: 'white', marginBottom: '0.25rem' }}>98%</div>
                            <div style={{ fontSize: '0.875rem', color: '#d1d5db' }}>Satisfaction</div>
                        </div>

                        <div style={{ textAlign: 'center' }}>
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: '4rem',
                                height: '4rem',
                                backgroundColor: 'rgba(249, 115, 22, 0.2)',
                                borderRadius: '50%',
                                marginBottom: '1rem',
                                margin: '0 auto 1rem auto'
                            }}>
                                <FaBuilding style={{ fontSize: '1.5rem', color: '#fb923c' }} />
                            </div>
                            <div style={{ fontSize: '1.875rem', fontWeight: 'bold', color: 'white', marginBottom: '0.25rem' }}>150+</div>
                            <div style={{ fontSize: '0.875rem', color: '#d1d5db' }}>Projets</div>
                        </div>
                    </div>
                </div>

                {/* Right Side - Login Modal */}
                <div style={{
                    width: isLargeScreen ? '50%' : '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '2rem'
                }}>
                    <div style={{
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: '1rem',
                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                        padding: '2rem',
                        width: '100%',
                        maxWidth: '28rem',
                        position: 'relative'
                    }}>
                        {/* Close Button (decorative) */}
                        <button style={{
                            position: 'absolute',
                            top: '1rem',
                            right: '1rem',
                            color: '#9ca3af',
                            background: 'none',
                            border: 'none',
                            cursor: 'pointer',
                            transition: 'color 0.2s'
                        }}>
                            <FaTimes style={{ fontSize: '1.125rem' }} />
                        </button>

                        {/* Logo */}
                        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                            <div style={{
                                width: '4rem',
                                height: '4rem',
                                background: 'linear-gradient(90deg, #3b82f6 0%, #9333ea 100%)',
                                borderRadius: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                margin: '0 auto 1rem auto'
                            }}>
                                <FaUsers style={{ color: 'white', fontSize: '1.5rem' }} />
                            </div>
                            <h2 style={{
                                fontSize: '1.5rem',
                                fontWeight: 'bold',
                                color: '#1f2937',
                                marginBottom: '0.5rem'
                            }}>Connexion/Inscription</h2>
                            <p style={{
                                fontSize: '0.875rem',
                                color: '#6b7280'
                            }}>
                                Après l'inscription, c'est à dire que vous acceptez notre{' '}
                                <span style={{ color: '#3b82f6', cursor: 'pointer' }}>Politique de Confidentialité</span>
                            </p>
                        </div>

                        {/* Error Message */}
                        {error && (
                            <div style={{
                                backgroundColor: '#fef2f2',
                                border: '1px solid #fecaca',
                                color: '#b91c1c',
                                padding: '0.75rem 1rem',
                                borderRadius: '0.5rem',
                                marginBottom: '1.5rem'
                            }}>
                                {error}
                            </div>
                        )}

                        {/* Login Form */}
                        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                            {/* Email Field */}
                            <div>
                                <label htmlFor="email" style={{
                                    display: 'block',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    color: '#374151',
                                    marginBottom: '0.5rem'
                                }}>
                                    E-mail
                                </label>
                                <input
                                    id="email"
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '0.75rem 1rem',
                                        border: '1px solid #d1d5db',
                                        borderRadius: '0.5rem',
                                        fontSize: '1rem',
                                        transition: 'all 0.2s',
                                        outline: 'none'
                                    }}
                                    placeholder="<EMAIL>"
                                    onFocus={(e) => {
                                        e.target.style.borderColor = '#3b82f6';
                                        e.target.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.2)';
                                    }}
                                    onBlur={(e) => {
                                        e.target.style.borderColor = '#d1d5db';
                                        e.target.style.boxShadow = 'none';
                                    }}
                                />
                            </div>

                            {/* Password Field */}
                            <div>
                                <label htmlFor="password" style={{
                                    display: 'block',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    color: '#374151',
                                    marginBottom: '0.5rem'
                                }}>
                                    Mot de Passe
                                </label>
                                <div style={{ position: 'relative' }}>
                                    <input
                                        id="password"
                                        type={showPassword ? "text" : "password"}
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        required
                                        style={{
                                            width: '100%',
                                            padding: '0.75rem 3rem 0.75rem 1rem',
                                            border: '1px solid #d1d5db',
                                            borderRadius: '0.5rem',
                                            fontSize: '1rem',
                                            transition: 'all 0.2s',
                                            outline: 'none'
                                        }}
                                        placeholder="Veuillez saisir le mot de passe"
                                        onFocus={(e) => {
                                            e.target.style.borderColor = '#3b82f6';
                                            e.target.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.2)';
                                        }}
                                        onBlur={(e) => {
                                            e.target.style.borderColor = '#d1d5db';
                                            e.target.style.boxShadow = 'none';
                                        }}
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        style={{
                                            position: 'absolute',
                                            right: '0.75rem',
                                            top: '50%',
                                            transform: 'translateY(-50%)',
                                            color: '#9ca3af',
                                            background: 'none',
                                            border: 'none',
                                            cursor: 'pointer',
                                            transition: 'color 0.2s'
                                        }}
                                    >
                                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                                    </button>
                                </div>
                                <div style={{ textAlign: 'right', marginTop: '0.5rem' }}>
                                    <a href="#" style={{
                                        fontSize: '0.875rem',
                                        color: '#3b82f6',
                                        textDecoration: 'none',
                                        transition: 'color 0.2s'
                                    }}>
                                        Mot de passe oublié ?
                                    </a>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <button
                                type="submit"
                                disabled={isLoading}
                                style={{
                                    width: '100%',
                                    background: isLoading ? '#9ca3af' : 'linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)',
                                    color: 'white',
                                    padding: '0.75rem 1rem',
                                    border: 'none',
                                    borderRadius: '0.5rem',
                                    fontWeight: '500',
                                    fontSize: '1rem',
                                    cursor: isLoading ? 'not-allowed' : 'pointer',
                                    transition: 'all 0.2s',
                                    opacity: isLoading ? 0.5 : 1
                                }}
                                onMouseEnter={(e) => {
                                    if (!isLoading) {
                                        e.target.style.background = 'linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%)';
                                    }
                                }}
                                onMouseLeave={(e) => {
                                    if (!isLoading) {
                                        e.target.style.background = 'linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)';
                                    }
                                }}
                            >
                                {isLoading ? "Connexion..." : "Se connecter"}
                            </button>
                        </form>


                    </div>
                </div>
            </div>
        </div>
    );
}

export default Login
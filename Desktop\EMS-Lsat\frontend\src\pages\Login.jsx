import React, { useState } from 'react';
import axios from 'axios';
import { useAuth } from '../context/authContext.jsx';
import { useNavigate } from 'react-router-dom';
import { FaEye, FaEyeSlash, FaUsers, FaBuilding, FaChartLine, FaTimes } from 'react-icons/fa';

const Login = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [error, setError] = useState(null);
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const { login } = useAuth();
    const navigate = useNavigate();

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);
        setError(null);

        try {
            const response = await axios.post('http://localhost:4000/api/auth/login', { email, password });

            if (response.data.success) {
                login(response.data.utilisateur);
                localStorage.setItem('token', response.data.token);

                if (response.data.utilisateur.role === 'admin') {
                    navigate('/AdminPage');
                } else if (response.data.utilisateur.role === 'rh') {
                    navigate('/RHpage');
                } else {
                    navigate('/employee-page');
                }
            }
        } catch (error) {
            if (error.response && !error.response.data.success) {
                setError(error.response.data.message);
            } else {
                setError("Erreur de connexion au serveur");
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
            {/* Background Image with Overlay */}
            <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                style={{
                    backgroundImage: 'url(/Female-human-resource-manager-shaking-hands-with-newly-hired-employees.jpg)',
                    filter: 'brightness(0.3) blur(1px)'
                }}
            />

            {/* Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-slate-900/80 via-purple-900/70 to-slate-900/80" />

            {/* Content */}
            <div className="relative z-10 min-h-screen flex">
                {/* Left Side - Statistics and Branding */}
                <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-start p-12 text-white">
                    {/* Logo/Brand */}
                    <div className="mb-12">
                        <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                            EMS Platform
                        </h1>
                        <p className="text-xl text-gray-300">Système de Gestion des Employés</p>
                    </div>

                    {/* Statistics Grid */}
                    <div className="grid grid-cols-2 gap-8 w-full max-w-md">
                        <div className="text-center">
                            <div className="flex items-center justify-center w-16 h-16 bg-blue-500/20 rounded-full mb-4 mx-auto">
                                <FaUsers className="text-2xl text-blue-400" />
                            </div>
                            <div className="text-3xl font-bold text-white mb-1">500+</div>
                            <div className="text-sm text-gray-300">Employés</div>
                        </div>

                        <div className="text-center">
                            <div className="flex items-center justify-center w-16 h-16 bg-purple-500/20 rounded-full mb-4 mx-auto">
                                <FaBuilding className="text-2xl text-purple-400" />
                            </div>
                            <div className="text-3xl font-bold text-white mb-1">25+</div>
                            <div className="text-sm text-gray-300">Départements</div>
                        </div>

                        <div className="text-center">
                            <div className="flex items-center justify-center w-16 h-16 bg-green-500/20 rounded-full mb-4 mx-auto">
                                <FaChartLine className="text-2xl text-green-400" />
                            </div>
                            <div className="text-3xl font-bold text-white mb-1">98%</div>
                            <div className="text-sm text-gray-300">Satisfaction</div>
                        </div>

                        <div className="text-center">
                            <div className="flex items-center justify-center w-16 h-16 bg-orange-500/20 rounded-full mb-4 mx-auto">
                                <FaBuilding className="text-2xl text-orange-400" />
                            </div>
                            <div className="text-3xl font-bold text-white mb-1">150+</div>
                            <div className="text-sm text-gray-300">Projets</div>
                        </div>
                    </div>
                </div>

                {/* Right Side - Login Modal */}
                <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
                    <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 w-full max-w-md relative">
                        {/* Close Button (decorative) */}
                        <button className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors">
                            <FaTimes className="text-lg" />
                        </button>

                        {/* Logo */}
                        <div className="text-center mb-8">
                            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <FaUsers className="text-white text-2xl" />
                            </div>
                            <h2 className="text-2xl font-bold text-gray-800 mb-2">Connexion/Inscription</h2>
                            <p className="text-sm text-gray-600">
                                Après l'inscription, c'est à dire que vous acceptez notre{' '}
                                <span className="text-blue-500 cursor-pointer">Politique de Confidentialité</span>
                            </p>
                        </div>

                        {/* Error Message */}
                        {error && (
                            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                                {error}
                            </div>
                        )}

                        {/* Login Form */}
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Email Field */}
                            <div>
                                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                                    E-mail
                                </label>
                                <input
                                    id="email"
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    required
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                    placeholder="<EMAIL>"
                                />
                            </div>

                            {/* Password Field */}
                            <div>
                                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                                    Mot de Passe
                                </label>
                                <div className="relative">
                                    <input
                                        id="password"
                                        type={showPassword ? "text" : "password"}
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        required
                                        className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                        placeholder="Veuillez saisir le mot de passe"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                                    >
                                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                                    </button>
                                </div>
                                <div className="text-right mt-2">
                                    <a href="#" className="text-sm text-blue-500 hover:text-blue-700">
                                        Mot de passe oublié ?
                                    </a>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <button
                                type="submit"
                                disabled={isLoading}
                                className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isLoading ? "Connexion..." : "Se connecter"}
                            </button>
                        </form>

                        {/* Footer */}
                        <div className="mt-6 text-center">
                            <p className="text-sm text-gray-600 mb-4">
                                Avez-vous besoin d'un compte ? <span className="text-blue-500 cursor-pointer">S'inscrire</span>
                            </p>
                            <div className="text-center">
                                <span className="text-sm text-gray-500">OU</span>
                            </div>
                            <button className="mt-4 w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                                <span className="text-gray-700">Google</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Login
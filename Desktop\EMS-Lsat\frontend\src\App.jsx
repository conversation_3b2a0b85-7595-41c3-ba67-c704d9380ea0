import { BrowserRouter , Routes , Route , Navigate } from "react-router-dom";
import Login from "./pages/Login";
import RHpage from "./pages/RHpage";
import AdminPage from "./pages/AdminPage";
import Employeepage from "./pages/Employeepage";
import RoleBaseRoutes from "./utils/RoleBaseRoutes";
import PrivateRoute from "./utils/PrivateRoute";
import { NotificationProvider } from "./contexts/NotificationContext";
import RhSummary from "./components/dashboard/RhSammary";
import AdminSummary from "./components/dashboard/AdminSummary";
import DepartmentList from "./components/departement/DepartmentList";
import DepartmentServiceManagement from "./components/admin/DepartmentServiceManagement";
import DepartmentServiceDashboard from "./components/admin/DepartmentServiceDashboard";
import ContratList from "./components/contrats/ContratList";
import ProjetList from "./components/projets/ProjetList";
import ArchivesList from "./components/projets/ArchivesList";
import EmployeeList from "./components/employes/EmployeeList";

import AttestationsList from "./components/attestations/AttestationsList";
import TachesList from "./components/taches/TachesList";
import FormationManagement from "./components/formations/FormationManagement";
import FormationManager from "./components/formations/FormationManager";
import FormationTest from "./components/formations/FormationTest";
import PointageList from "./components/pointage/PointageList";
import MessagesList from "./components/messages/MessagesList";
import DocumentsList from "./components/documents/DocumentsList";

function App() {


  return (
   <NotificationProvider>
     <BrowserRouter>
        <Routes>
        <Route path="/" element={<Navigate to="/Login" />}/>
        <Route path="/Login" element={<Login />}/>

        {/* Routes RH */}
        <Route path="/RHpage" element={
          <PrivateRoute>
            <RoleBaseRoutes requiredRole="rh">
              <RHpage />
          </RoleBaseRoutes>
          </PrivateRoute >
          }>
          <Route index element={<RhSummary />} ></Route>
          <Route path="employes" element={<EmployeeList />} />
          <Route path="departements" element={<DepartmentList />} />
          <Route path="conges" element={<div style={{padding: '2rem', backgroundColor: 'white', borderRadius: '8px'}}>Gestion des Congés - En développement</div>} />
          <Route path="contrats" element={<ContratList />} />
          <Route path="salaires" element={<div style={{padding: '2rem', backgroundColor: 'white', borderRadius: '8px'}}>Gestion des Salaires - En développement</div>} />
          <Route path="primes" element={<div style={{padding: '2rem', backgroundColor: 'white', borderRadius: '8px'}}>Gestion des Primes - En développement</div>} />
          <Route path="projets" element={<div style={{padding: '2rem', backgroundColor: 'white', borderRadius: '8px'}}>Gestion des Projets - En développement</div>} />
          <Route path="attestations" element={<AttestationsList />} />
          <Route path="taches" element={<TachesList />} />
          <Route path="formations" element={<FormationManager />} />
          <Route path="formations-old" element={<FormationManagement />} />
          <Route path="formations-test" element={<FormationTest />} />
          <Route path="pointage" element={<PointageList />} />
          <Route path="messages" element={<MessagesList />} />
          <Route path="documents" element={<DocumentsList />} />
        </Route>

        {/* Routes Admin */}
        <Route path="/AdminPage" element={
          <PrivateRoute>
            <RoleBaseRoutes requiredRole="admin">
              <AdminPage />
          </RoleBaseRoutes>
          </PrivateRoute >
          }>
          <Route index element={<AdminSummary />} ></Route>
          <Route path="utilisateurs" element={<div style={{padding: '2rem', backgroundColor: 'white', borderRadius: '8px'}}>Gestion des Utilisateurs - En développement</div>} />
          <Route path="employes" element={<EmployeeList />} />
          <Route path="departements" element={<DepartmentServiceDashboard />} />
          <Route path="contrats" element={<ContratList />} />
          <Route path="projets" element={<ProjetList />} />
          <Route path="archives" element={<ArchivesList />} />
          <Route path="messages" element={<MessagesList />} />
        </Route>

        {/* Routes Employé */}
        <Route path="/employee-page" element={
          <PrivateRoute>
            <RoleBaseRoutes requiredRole="employe">
              <Employeepage />
            </RoleBaseRoutes>
          </PrivateRoute>
        } />

      </Routes>
    </BrowserRouter>
   </NotificationProvider>
  )
}

export default App
